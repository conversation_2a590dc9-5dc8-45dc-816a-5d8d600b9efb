import { useEffect, useState } from "react";
import { Form, Input, Button, Spin, message, Row, Col, DatePicker, Typography, Space, Card, Tabs, Badge, Modal } from "antd";
import dayjs from "dayjs";
import {
  getEmployeeDetail,
  editEmployee,
  addEmployee,
  completeEmployee,
  Employee,
  createEmptyEmployee,
} from "@/services/pas/EmployeeService";
import {
  getIdTypeOptions,
  getIdErrorOptions,
  getBloodTypeOptions,
} from "@/services/pas/OptionParameterService";
import ApiSelect from "@/app/pas/components/ApiSelect";
import {
  IdcardOutlined,
  UserOutlined,
  PhoneOutlined,
  HomeOutlined,
  HeartOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import '@/app/pas/styles/form.css';

const { Title } = Typography;

type EditEmployeeFormProps = {
  userId: string;
  mode: string;
  visible: boolean;
  onSuccess: () => void;
  onCancel: () => void;
};

const EditEmployeeForm: React.FC<EditEmployeeFormProps> = ({
  userId,
  mode,
  visible,
  onSuccess,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [initialData, setInitialData] = useState<Employee>(createEmptyEmployee());
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("basic");
  const [formErrors, setFormErrors] = useState<Record<string, boolean>>({
    account: false,
    basic: false,
    contact: false,
    family: false,
    other: false,
  });

  // === 1. Tab 定義錯誤對應表 ===
  const TAB_FIELD_MAP: Record<string, Array<string | RegExp>> = {
    account: [/^usersDTO\.account$/, /^usersDTO\.password$/],
    basic: ['empNo', 'idNo', 'idType', 'errorMark', 'usersDTO.name', 'birthday', 'bloodType'],
    contact: ['usersDTO.eMail', 'usersDTO.telNo', 'usersDTO.phone', 'usersDTO.altPhone', 'usersDTO.permanentAddress', 'usersDTO.mailingAddress'],
    family: ['spouseName', 'spouseIdNo'],
    other: ['remark'],
  };

  // === 2. 初始值產生封裝 ===
  const getInitialValues = (data: Employee) => ({
    ...data,
    birthday: data.birthday ? dayjs(data.birthday) : null,
    usersDTO: {
      ...data.usersDTO,
    },
  });

  // 重置表單和狀態
  const resetForm = (currentMode?: string) => {
    form.resetFields();
    setActiveTab(currentMode === "add" ? "account" : "basic");
    setFormErrors({
      account: false,
      basic: false,
      contact: false,
      family: false,
      other: false,
    });
  };

  // 初始化或模式改變時的處理
  useEffect(() => {
    if (visible) {
      resetForm(mode);
      if (mode === "edit" || mode === "complete") {
        fetchEmployee();
      } else {
        const empty = createEmptyEmployee();
        setInitialData(empty);
        form.setFieldsValue(empty);
      }
    }
  }, [visible, mode]);

  const fetchEmployee = async () => {
    setLoading(true);
    try {
      const { success, data, message: msg } = await getEmployeeDetail(userId);
      if (success && data) {
        setInitialData(data);
        form.setFieldsValue({
          ...data,
          birthday: data.birthday ? dayjs(data.birthday) : null,
          usersDTO: {
            ...data.usersDTO,
          },
        });
      } else {
        message.error(msg || "載入資料失敗");
      }
    } catch (error: any) {
      message.error(error.message || "載入失敗");
    } finally {
      setLoading(false);
    }
  };

  const handleFinish = async (values: any) => {
    // 在提交時進行表單驗證並更新錯誤狀態
    handleFormChange();

    const payload: Employee = {
      ...initialData,
      ...values,
      birthday: values.birthday
        ? dayjs(values.birthday).format("YYYY-MM-DD")
        : "",
      usersDTO: {
        ...initialData.usersDTO,
        ...values.usersDTO,
      },
    };

    setSaving(true);
    try {
      let response;
      if (mode === "edit") {
        response = await editEmployee(payload);
      } else if (mode === "add") {
        response = await addEmployee(payload);
      } else if (mode === "complete") {
        response = await completeEmployee(payload);
      }

      if (response?.success && response.data?.result) {
        message.success(
          mode === "add"
            ? "新增成功"
            : mode === "edit"
              ? "儲存成功"
              : "補全成功"
        );
        onSuccess();
        resetForm(mode);
      } else {
        message.error(response?.data?.msg || "儲存失敗");
      }
    } catch (error: any) {
      message.error(error.message || "儲存時發生錯誤");
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    resetForm(mode);
    onCancel();
  };

  // === 3. handleFormChange 精簡化並改進錯誤檢測 ===
  // 當表單有欄位錯誤改變時呼叫，用來判斷每個分頁 (tab) 是否出現錯誤
  const handleFormChange = () => {
    // 取得所有欄位目前的錯誤狀態（每個欄位會有 name 和 errors 陣列）
    const fields = form.getFieldsError();

    // 初始化每個分頁（tab）都標記為「沒有錯誤」
    const newErrors = Object.fromEntries(
      Object.keys(TAB_FIELD_MAP).map(key => [key, false])
    );

    // 遍歷所有有驗證錯誤的欄位
    fields.forEach(({ name, errors }) => {
      // 如果這個欄位有錯誤訊息（errors 陣列不是空的）
      if (errors.length > 0) {
        // 把欄位的 name 轉換成像 "usersDTO.account" 這樣的字串路徑
        const fieldPath = Array.isArray(name) ? name.join('.') : name;

        // 逐一檢查每個 tab 的欄位設定，看這個錯誤欄位屬於哪一個 tab
        for (const [tab, patterns] of Object.entries(TAB_FIELD_MAP)) {
          // 判斷目前這個欄位的名稱是否符合該 tab 所定義的欄位條件
          const hasError = patterns.some(pattern => {
            // 如果 pattern 是正則表達式，就用 test() 來比對
            if (pattern instanceof RegExp) {
              return pattern.test(fieldPath);
            }
            // 否則直接用字串比對欄位名稱
            return pattern === fieldPath;
          });

          // 如果這個欄位屬於某個 tab，則標記該 tab 為有錯誤，並停止繼續檢查
          if (hasError) {
            newErrors[tab] = true;
            break; // 找到就不用繼續檢查其他 tab
          }
        }
      }
    });

    // 更新 state：哪個 tab 有錯就會在畫面顯示紅點
    setFormErrors(newErrors);
  };

  // === 4. tabItems 結構封裝為函式 ===
  const getTabItems = () => {
    const tabs = [
      ...(mode === "add" ? [{
        key: 'account',
        icon: <UserOutlined />,
        label: '帳號設定',
        content: (
          <Row gutter={[24, 16]}>
            <Col md={12}><Form.Item label="帳號" name={["usersDTO", "account"]} rules={[{ required: true, message: "請輸入帳號" }]}><Input placeholder="請輸入帳號" /></Form.Item></Col>
            <Col md={12}><Form.Item label="密碼" name={["usersDTO", "password"]} rules={[{ required: true, message: "請輸入密碼" }]}><Input.Password placeholder="請輸入密碼" /></Form.Item></Col>
          </Row>
        ),
      }] : []),
      {
        key: 'basic',
        icon: <IdcardOutlined />,
        label: '基本資料',
        content: (
          <Row gutter={[24, 16]}>
            <Col md={12}><Form.Item label="員工編號" name="empNo" rules={[{ required: true, message: "請輸入員工編號" }]}><Input placeholder="請輸入員工編號" /></Form.Item></Col>
            <Col md={12}><Form.Item label="身分證字號" name="idNo" rules={[{ required: true, message: "請輸入身分證字號" }]}><Input placeholder="請輸入身分證字號" /></Form.Item></Col>
            <Col md={12}><Form.Item label="證號別" name="idType"><ApiSelect fetchOptions={getIdTypeOptions} placeholder="請選擇證號別" /></Form.Item></Col>
            <Col md={12}><Form.Item label="證號錯誤註記" name="errorMark"><ApiSelect fetchOptions={getIdErrorOptions} placeholder="請選擇錯誤註記" /></Form.Item></Col>
            <Col md={12}><Form.Item label="姓名" name={["usersDTO", "name"]} rules={[{ required: true, message: "請輸入姓名" }]}><Input /></Form.Item></Col>
            <Col md={12}><Form.Item label="生日" name="birthday"><DatePicker style={{ width: "100%" }} /></Form.Item></Col>
            <Col md={12}><Form.Item label="血型" name="bloodType"><ApiSelect fetchOptions={getBloodTypeOptions} /></Form.Item></Col>
          </Row>
        ),
      },
      {
        key: 'contact',
        icon: <PhoneOutlined />,
        label: '聯絡資訊',
        content: (
          <Row gutter={[24, 16]}>
            <Col md={12}><Form.Item label="Email" name={["usersDTO", "eMail"]}><Input /></Form.Item></Col>
            <Col md={12}><Form.Item label="電話" name={["usersDTO", "telNo"]}><Input /></Form.Item></Col>
            <Col md={12}><Form.Item label="手機" name={["usersDTO", "phone"]}><Input /></Form.Item></Col>
            <Col md={12}><Form.Item label="其他聯絡電話" name={["usersDTO", "altPhone"]}><Input /></Form.Item></Col>
            <Col span={24}><Form.Item label="戶籍地址" name={["usersDTO", "permanentAddress"]}><Input /></Form.Item></Col>
            <Col span={24}><Form.Item label="通訊地址" name={["usersDTO", "mailingAddress"]}><Input /></Form.Item></Col>
          </Row>
        ),
      },
      {
        key: 'family',
        icon: <HomeOutlined />,
        label: '眷屬資訊',
        content: (
          <Row gutter={[24, 16]}>
            <Col md={12}><Form.Item label="配偶姓名" name="spouseName"><Input /></Form.Item></Col>
            <Col md={12}><Form.Item label="配偶身分證" name="spouseIdNo"><Input /></Form.Item></Col>
          </Row>
        ),
      },
      {
        key: 'other',
        icon: <FileTextOutlined />,
        label: '其他資訊',
        content: (
          <Row gutter={[24, 16]}>
            <Col span={24}><Form.Item label="備註" name="remark"><Input.TextArea rows={5} /></Form.Item></Col>
          </Row>
        ),
      },
    ];

    return tabs.map(({ key, label, content, icon }) => ({
      key,
      label: (
        <Badge dot={formErrors[key]}>
          <Space>{icon}{label}</Space>
        </Badge>
      ),
      children: content,
    }));
  };

  return (
    <Modal
      title={
        mode === "add"
          ? "新增員工資料"
          : mode === "edit"
            ? "編輯員工資料"
            : "補全員工資料"
      }
      open={visible}
      onCancel={handleCancel}
      width={800}
      footer={null}
    >
      {loading ? (
        <div style={{ textAlign: "center", padding: 20 }}>
          <Spin>
            <div style={{ padding: "20px" }}>載入中...</div>
          </Spin>
        </div>
      ) : (
        <Form
          layout="vertical"
          form={form}
          initialValues={mode === "add" ? createEmptyEmployee() : getInitialValues(initialData)}
          onFinish={handleFinish}
          onFieldsChange={handleFormChange}
        >
          <Card>
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={getTabItems()}
              type="card"
            />
            <div style={{ textAlign: "right", marginTop: "24px" }}>
              <Button onClick={handleCancel} style={{ marginRight: 8 }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={saving}>
                {mode === "add" ? "新增" : "儲存"}
              </Button>
            </div>
          </Card>
        </Form>
      )}
    </Modal>
  );
};

export default EditEmployeeForm;
