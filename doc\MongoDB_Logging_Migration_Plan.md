# FastERP MongoDB 日誌系統遷移計畫

## 📋 **遷移概述**

### **目標**
將現有的 MongoDB 日誌系統從不穩定的架構遷移到新的彈性、可靠的日誌系統，解決循環引用、連接失敗和資料序列化問題。

### **遷移範圍**
- **服務層**: 從 `MongoDBLoggerService` 遷移到 `ResilientMongoDBLoggerService`
- **資料模型**: 從複雜的 `MongoDBLogEntry` 遷移到簡化的 `SimpleLogEntry`
- **連接管理**: 引入專用的 `MongoDBConnectionManager`
- **資料處理**: 引入 `LogDataProcessor` 進行安全序列化
- **錯誤處理**: 實現熔斷器模式和降級機制

---

## 🗓️ **遷移時程表**

### **階段 1: 準備階段 (1-2 天)**
- [ ] 備份現有 MongoDB 日誌資料
- [ ] 建立測試環境
- [ ] 驗證新系統組件編譯
- [ ] 準備回滾計畫

### **階段 2: 並行部署 (2-3 天)**
- [ ] 部署新服務但保持舊服務運行
- [ ] 配置雙寫模式（同時寫入新舊系統）
- [ ] 監控新系統穩定性
- [ ] 驗證資料一致性

### **階段 3: 切換階段 (1 天)**
- [ ] 停止寫入舊系統
- [ ] 完全切換到新系統
- [ ] 監控系統運行狀況
- [ ] 驗證所有功能正常

### **階段 4: 清理階段 (已完成)**
- [x] 移除舊系統程式碼
- [x] 清理舊的依賴注入配置
- [x] 更新文檔
- [x] 完成遷移驗證

---

## 🔧 **詳細遷移步驟**

### **步驟 1: 服務註冊更新**

#### **1.1 更新 Program.cs**
```csharp
// 新增服務註冊
builder.Services.AddSingleton<IMongoDBConnectionManager, MongoDBConnectionManager>();
builder.Services.AddSingleton<ILogDataProcessor, LogDataProcessor>();
builder.Services.AddSingleton<LogProcessingOptions>();

// 暫時保留舊服務，添加新服務
builder.Services.AddSingleton<IResilientLoggerService, ResilientMongoDBLoggerService>();

// 階段性替換：先註冊新服務為不同介面
// builder.Services.AddSingleton<ILoggerService, ResilientMongoDBLoggerService>();
```

#### **1.2 建立服務工廠模式**
```csharp
public interface ILoggerServiceFactory
{
    ILoggerService GetLegacyService();
    IResilientLoggerService GetResilientService();
}
```

### **步驟 2: 資料庫準備**

#### **2.1 建立新集合**
```javascript
// MongoDB 指令
use FAST_ERP
db.createCollection("Logger_New")
db.Logger_New.createIndex({ "timestamp": -1 })
db.Logger_New.createIndex({ "level": 1 })
db.Logger_New.createIndex({ "entityType": 1, "entityId": 1 })
db.Logger_New.createIndex({ "transactionId": 1 })
```

#### **2.2 資料遷移腳本**
```javascript
// 遷移現有資料到新格式
db.Logger.find().forEach(function(doc) {
    var newDoc = {
        _id: doc._id,
        timestamp: doc.CreateTime || new Date(),
        level: doc.LogLevel || "Information",
        message: doc.Message || "",
        source: doc.Source || "System",
        transactionId: doc.TransactionId,
        data: {
            operation: "MIGRATED",
            summary: "從舊系統遷移的資料",
            beforeData: doc.Data ? JSON.stringify(doc.Data) : null,
            status: "Migrated"
        },
        userId: doc.UserId,
        ipAddress: doc.IpAddress,
        requestUrl: doc.RequestUrl,
        userAgent: doc.UserAgent,
        errorMessage: doc.Exception,
        stackTrace: doc.StackTrace
    };
    
    db.Logger_New.insertOne(newDoc);
});
```

### **步驟 3: 並行運行配置**

#### **3.1 雙寫適配器**
```csharp
public class DualWriteLoggerService : ILoggerService
{
    private readonly ILoggerService _legacyService;
    private readonly IResilientLoggerService _newService;
    private readonly ILogger<DualWriteLoggerService> _logger;
    private readonly bool _enableNewService;

    public async Task LogDataAsync<T>(string message, T changedData, string transactionId, string source = "System") where T : class
    {
        // 始終寫入舊系統（保證向後相容）
        try
        {
            await _legacyService.LogDataAsync(message, changedData, transactionId, source);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "舊日誌系統寫入失敗");
        }

        // 條件性寫入新系統
        if (_enableNewService)
        {
            try
            {
                await _newService.LogDataAsync(message, changedData, transactionId, source);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "新日誌系統寫入失敗");
            }
        }
    }
}
```

### **步驟 4: 測試驗證**

#### **4.1 功能測試**
```csharp
[Test]
public async Task TestNewLoggingSystem_BasicFunctionality()
{
    // 測試基本日誌記錄
    await _resilientLogger.LogInfoAsync("測試訊息", "TestSource");
    
    // 驗證日誌已正確寫入
    var healthStatus = await _resilientLogger.GetHealthStatusAsync();
    Assert.IsTrue(healthStatus.IsHealthy);
}

[Test]
public async Task TestNewLoggingSystem_EntityChanges()
{
    // 測試實體變更日誌
    var partner = new Partner { PartnerID = Guid.NewGuid(), Name = "測試夥伴" };
    await _resilientLogger.LogDataAsync("新增夥伴", partner, Guid.NewGuid().ToString());
    
    // 驗證資料格式正確
    // ... 驗證邏輯
}

[Test]
public async Task TestNewLoggingSystem_ErrorHandling()
{
    // 測試錯誤處理和熔斷器
    // 模擬 MongoDB 連接失敗
    // 驗證降級機制
}
```

#### **4.2 效能測試**
```csharp
[Test]
public async Task TestNewLoggingSystem_Performance()
{
    var stopwatch = Stopwatch.StartNew();
    var tasks = new List<Task>();
    
    // 並發測試
    for (int i = 0; i < 1000; i++)
    {
        tasks.Add(_resilientLogger.LogInfoAsync($"效能測試 {i}", "PerformanceTest"));
    }
    
    await Task.WhenAll(tasks);
    stopwatch.Stop();
    
    Assert.IsTrue(stopwatch.ElapsedMilliseconds < 10000); // 10秒內完成
}
```

### **步驟 5: 監控和驗證**

#### **5.1 健康檢查端點**
```csharp
[ApiController]
[Route("api/[controller]")]
public class LoggingHealthController : ControllerBase
{
    private readonly IResilientLoggerService _logger;

    [HttpGet("status")]
    public async Task<IActionResult> GetHealthStatus()
    {
        var status = await _logger.GetHealthStatusAsync();
        return Ok(status);
    }

    [HttpGet("statistics")]
    public async Task<IActionResult> GetErrorStatistics()
    {
        var stats = await _logger.GetErrorStatisticsAsync();
        return Ok(stats);
    }

    [HttpPost("test")]
    public async Task<IActionResult> TestLogging()
    {
        var success = await _logger.TryLogAsync(
            new SimpleLogEntry("Information", "健康檢查測試", "HealthCheck"));
        
        return Ok(new { Success = success });
    }
}
```

#### **5.2 監控指標**
- **成功率**: 目標 > 99.5%
- **平均回應時間**: 目標 < 100ms
- **熔斷器觸發次數**: 監控異常情況
- **降級模式啟用次數**: 監控系統穩定性

---

## 🔄 **回滾計畫**

### **回滾觸發條件**
- 新系統成功率 < 95%
- 平均回應時間 > 500ms
- 發生資料遺失
- 業務功能受到影響

### **回滾步驟**
1. **立即停用新系統**
   ```csharp
   // 在 appsettings.json 中設定
   "Logging": {
     "UseResilientLogger": false
   }
   ```

2. **恢復舊系統服務註冊**
   ```csharp
   // 在 Program.cs 中
   builder.Services.AddSingleton<ILoggerService, MongoDBLoggerService>();
   ```

3. **驗證舊系統功能**
4. **分析失敗原因**
5. **準備修復計畫**

---

## 📊 **驗證檢查清單**

### **功能驗證**
- [ ] 基本日誌記錄功能正常
- [ ] 實體變更日誌正確記錄
- [ ] 錯誤日誌包含完整資訊
- [ ] 批次操作日誌正確處理
- [ ] 上下文資訊正確填充

### **效能驗證**
- [ ] 單一日誌記錄 < 50ms
- [ ] 批次日誌記錄 < 200ms (100條)
- [ ] 並發處理能力 > 1000 TPS
- [ ] 記憶體使用量穩定
- [ ] CPU 使用率合理

### **可靠性驗證**
- [ ] MongoDB 連接失敗時降級正常
- [ ] 熔斷器機制正確觸發
- [ ] 重試機制有效
- [ ] 資料不會遺失
- [ ] 業務操作不受影響

### **資料完整性驗證**
- [ ] 日誌格式正確
- [ ] 時間戳準確
- [ ] 實體變更資料完整
- [ ] 敏感資料正確過濾
- [ ] 循環引用問題解決

---

## 🎯 **成功標準**

### **技術指標**
- 系統可用性 > 99.9%
- 日誌記錄成功率 > 99.5%
- 平均回應時間 < 100ms
- 零資料遺失
- 零業務影響

### **業務指標**
- 所有現有功能正常運作
- 審計追蹤完整可靠
- 系統效能無明顯下降
- 開發團隊滿意度高
- 維護成本降低

---

## 📝 **遷移後任務**

### **立即任務**
- [ ] 移除舊系統程式碼
- [ ] 更新技術文檔
- [ ] 培訓開發團隊
- [ ] 建立監控告警

### **後續優化**
- [ ] 效能調優
- [ ] 功能增強
- [ ] 監控改進
- [ ] 自動化測試擴充

此遷移計畫確保了系統的平穩過渡，最小化風險，並提供了完整的驗證和回滾機制。
