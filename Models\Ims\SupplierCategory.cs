using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 供應商分類 </summary>
public class SupplierCategory : ModelBaseEntity
{
    /// <summary> 供應商分類編號 </summary>
    [Key]
    [Comment("供應商分類編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid SupplierCategoryID { get; set; }

    /// <summary> 名稱 </summary>
    [Required]
    [MaxLength(100)]
    [Comment("名稱")]
    [Column(TypeName = "nvarchar(100)")]
    public string Name { get; set; }

    /// <summary> 描述 </summary>
    [MaxLength(500)]
    [Comment("描述")]
    [Column(TypeName = "nvarchar(500)")]
    public string Description { get; set; }

    /// <summary> 父分類ID </summary>
    [Comment("父分類ID")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid? ParentID { get; set; }

    /// <summary> 排序 </summary>
    [Comment("排序")]
    [Column(TypeName = "int")]
    public int SortCode { get; set; }

    /// <summary> 父分類 </summary>
    public SupplierCategory? Parent { get; set; }

    /// <summary> 子分類 </summary>
    public ICollection<SupplierCategory> Children { get; set; }

    /// <summary> 建構式 </summary>
    public SupplierCategory()
    {
        SupplierCategoryID = Guid.NewGuid();
        Name = string.Empty;
        Description = string.Empty;
        ParentID = null;
        SortCode = 0;
        Children = new List<SupplierCategory>();
    }
}

/// <summary> 供應商分類DTO </summary>
public class SupplierCategoryDTO : ModelBaseEntityDTO
{
    /// <summary> 供應商分類編號 </summary>
    public Guid SupplierCategoryID { get; set; }

    /// <summary> 名稱 </summary>
    public string Name { get; set; }

    /// <summary> 描述 </summary>
    public string Description { get; set; }

    /// <summary> 父分類ID </summary>
    public Guid? ParentID { get; set; }

    /// <summary> 排序 </summary>
    public int SortCode { get; set; }

    /// <summary> 父分類 </summary>
    public SupplierCategoryDTO? Parent { get; set; }

    /// <summary> 子分類 </summary>
    public ICollection<SupplierCategoryDTO> Children { get; set; }



    /// <summary> 建構式 </summary>
    public SupplierCategoryDTO()
    {
        SupplierCategoryID = Guid.NewGuid();
        Name = string.Empty;
        Description = string.Empty;
        ParentID = null;
        SortCode = 0;
        Children = new List<SupplierCategoryDTO>();
    }
}
