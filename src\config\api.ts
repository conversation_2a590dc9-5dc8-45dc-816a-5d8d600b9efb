// API 基礎路徑
//export const BASE_URL = "http://localhost:5015/";
export const BASE_URL = "https://localhost:7137/";
//export const BASE_URL = "http://localhost:8686/";
export const API_BASE_URL = `${BASE_URL}api`;

// API 端點配置
export const apiEndpoints = {
    /**
     * Common 通用
     * **/
    // Login 登入管理
    login: `${API_BASE_URL}/Login/VerifyLogin`,

    // Users 使用者資料管理
    getUsers: `${API_BASE_URL}/Users/<USER>
    getMyInfo: `${API_BASE_URL}/Users/<USER>
    getUserDetail: `${API_BASE_URL}/Users/<USER>
    addUser: `${API_BASE_URL}/Users/<USER>
    editUsers: `${API_BASE_URL}/Users/<USER>
    deleteUser: `${API_BASE_URL}/Users/<USER>
    changePassword: `${API_BASE_URL}/Users/<USER>

    // AuditLogs 審核日誌管理
    getAuditLogs: `${API_BASE_URL}/AuditLogs/GetAuditLogs`,
    getAuditLogDetail: `${API_BASE_URL}/AuditLogs/GetAuditLogs`,
    addAuditLog: `${API_BASE_URL}/AuditLogs/AddAuditLogs`,

    // City 縣市資料管理
    getCities: `${API_BASE_URL}/City/GetCity`,
    getCityDetail: `${API_BASE_URL}/City/GetCity`,
    addCity: `${API_BASE_URL}/City/AddCity`,
    editCity: `${API_BASE_URL}/City/EditCity`,
    deleteCity: `${API_BASE_URL}/City/DeleteCity`,

    // Department 部門資料管理
    getDepartments: `${API_BASE_URL}/Department/GetDepartment`,
    getDepartmentDetail: `${API_BASE_URL}/Department/GetDepartment`,
    addDepartment: `${API_BASE_URL}/Department/AddDepartment`,
    editDepartment: `${API_BASE_URL}/Department/EditDepartment`,
    deleteDepartment: `${API_BASE_URL}/Department/DeleteDepartment`,

    // District 鄉鎮市區資料管理
    getDistricts: `${API_BASE_URL}/District/GetDistrict`,
    getDistrictDetail: `${API_BASE_URL}/District/GetDistrict`,
    getDistrictByCity: `${API_BASE_URL}/District/GetDistrictByCity`,
    addDistrict: `${API_BASE_URL}/District/AddDistrict`,
    editDistrict: `${API_BASE_URL}/District/EditDistrict`,
    deleteDistrict: `${API_BASE_URL}/District/DeleteDistrict`,

    // Division 組別資料管理
    getDivisions: `${API_BASE_URL}/Division/GetDivision`,
    getDivisionDetail: `${API_BASE_URL}/Division/GetDivision`,
    addDivision: `${API_BASE_URL}/Division/AddDivision`,
    editDivision: `${API_BASE_URL}/Division/EditDivision`,
    deleteDivision: `${API_BASE_URL}/Division/DeleteDivision`,

    // EnterpriseGroups 管理公司群組
    getEnterpriseGroups: `${API_BASE_URL}/EnterpriseGroups/GetEnterpriseGroupsList`,
    getEnterpriseGroupDetail: `${API_BASE_URL}/EnterpriseGroups/GetEnterpriseGroupsDetail`,
    addEnterpriseGroup: `${API_BASE_URL}/EnterpriseGroups/AddEnterpriseGroups`,
    editEnterpriseGroup: `${API_BASE_URL}/EnterpriseGroups/EditEnterpriseGroups`,
    deleteEnterpriseGroup: `${API_BASE_URL}/EnterpriseGroups/DeleteEnterpriseGroups`,

    // JobRank 職級資料管理
    getJobRanks: `${API_BASE_URL}/JobRank/GetJobRank`,
    getJobRankDetail: `${API_BASE_URL}/JobRank/GetJobRank`,
    addJobRank: `${API_BASE_URL}/JobRank/AddJobRank`,
    editJobRank: `${API_BASE_URL}/JobRank/EditJobRank`,
    deleteJobRank: `${API_BASE_URL}/JobRank/DeleteJobRank`,

    // Position 職稱資料管理
    getPositions: `${API_BASE_URL}/Position/GetPosition`,
    getPositionDetail: `${API_BASE_URL}/Position/GetPosition`,
    addPosition: `${API_BASE_URL}/Position/AddPosition`,
    editPosition: `${API_BASE_URL}/Position/EditPosition`,
    deletePosition: `${API_BASE_URL}/Position/DeletePosition`,

    // Roles 角色管理
    getRoles: `${API_BASE_URL}/Roles/GetRoles`,
    getRoleDetail: `${API_BASE_URL}/Roles/GetRoles`,
    addRole: `${API_BASE_URL}/Roles/AddRoles`,
    editRole: `${API_BASE_URL}/Roles/EditRoles`,
    deleteRole: `${API_BASE_URL}/Roles/DeleteRoles`,

    // RolesPermissions 角色權限管理
    getRolesPermissions: `${API_BASE_URL}/RolesPermissions/GetRolesPermissions`,
    getRolesPermissionDetail: `${API_BASE_URL}/RolesPermissions/GetRolesPermissions`,
    addRolesPermission: `${API_BASE_URL}/RolesPermissions/AddRolesPermissions`,
    editRolesPermission: `${API_BASE_URL}/RolesPermissions/EditRolesPermissions`,
    deleteRolesPermission: `${API_BASE_URL}/RolesPermissions/DeleteRolesPermissions`,

    // SystemGroups 系統群組管理
    getSystemGroups: `${API_BASE_URL}/SystemGroups/GetSystemGroups`,
    addSystemGroup: `${API_BASE_URL}/SystemGroups/AddSystemGroups`,
    editSystemGroup: `${API_BASE_URL}/SystemGroups/EditSystemGroups`,
    deleteSystemGroup: `${API_BASE_URL}/SystemGroups/DeleteSystemmGroups`,

    // SystemMenu 系統選單管理
    getSystemMenus: `${API_BASE_URL}/SystemMenu/GetSystemMenu`,
    getSystemMenuDetail: `${API_BASE_URL}/SystemMenu/GetSystemMenu`,
    addSystemMenu: `${API_BASE_URL}/SystemMenu/AddSystemMenu`,
    editSystemMenu: `${API_BASE_URL}/SystemMenu/EditSystemMenu`,
    deleteSystemMenu: `${API_BASE_URL}/SystemMenu/DeleteSystemMenu`,
    getAllMenu: `${API_BASE_URL}/SystemMenu/GetAllMenu`,
    getMyAllMenu: `${API_BASE_URL}/SystemMenu/GetMyAllMenu`,

    // Unit 單位資料管理
    getUnits: `${API_BASE_URL}/Unit/GetAll`,
    getUnitById: `${API_BASE_URL}/Unit/Get`,
    addUnit: `${API_BASE_URL}/Unit/Add`,
    editUnit: `${API_BASE_URL}/Unit/Edit`,
    deleteUnit: `${API_BASE_URL}/Unit/Delete`,

    // 訊息相關
    getUnreadMessages: `${API_BASE_URL}/Messages/GetUnreadMessages`,
    getReadMessages: `${API_BASE_URL}/Messages/GetReadMessages`,
    markMessageAsRead: `${API_BASE_URL}/Messages/MarkAsRead`,

    // 通知相關
    getSystemNotifications: `${API_BASE_URL}/Notifications/GetSystemNotifications`,
    getPersonalNotifications: `${API_BASE_URL}/Notifications/GetPersonalNotifications`,
    markNotificationAsRead: `${API_BASE_URL}/Notifications/MarkAsRead`,

    // 公司圖片
    getEnterpriseImages: `${API_BASE_URL}/EnterpriseImage/GetList`,
    getEnterpriseImage: `${API_BASE_URL}/EnterpriseImage/Get`,
    uploadEnterpriseImage: `${API_BASE_URL}/EnterpriseImage/Upload`,
    deleteEnterpriseImage: `${API_BASE_URL}/EnterpriseImage/Delete`,

    /**
     * Pms 財產管理
     * **/
    // AccessoryEquipment 附屬設備管理
    getAccessoryEquipments: `${API_BASE_URL}/AccessoryEquipment/GetAll`,
    getAccessoryEquipmentDetail: `${API_BASE_URL}/AccessoryEquipment/Get`,
    addAccessoryEquipment: `${API_BASE_URL}/AccessoryEquipment/Add`,
    editAccessoryEquipment: `${API_BASE_URL}/AccessoryEquipment/Edit`,
    deleteAccessoryEquipment: `${API_BASE_URL}/AccessoryEquipment/Delete`,

    // AssetAccount 財產科目管理
    getAssetAccounts: `${API_BASE_URL}/AssetAccount/GetAll`,
    getAssetAccountDetail: `${API_BASE_URL}/AssetAccount/Get`,
    addAssetAccount: `${API_BASE_URL}/AssetAccount/Add`,
    editAssetAccount: `${API_BASE_URL}/AssetAccount/Edit`,
    deleteAssetAccount: `${API_BASE_URL}/AssetAccount/Delete`,

    // AssetCategory 財產類別管理
    getAssetCategories: `${API_BASE_URL}/AssetCategory/GetAll`,
    getAssetCategoryDetail: `${API_BASE_URL}/AssetCategory/Get`,
    addAssetCategory: `${API_BASE_URL}/AssetCategory/Add`,
    editAssetCategory: `${API_BASE_URL}/AssetCategory/Edit`,
    deleteAssetCategory: `${API_BASE_URL}/AssetCategory/Delete`,

    // Asset 財產資料管理
    getAssets: `${API_BASE_URL}/Asset/GetAll`,
    getAssetDetail: `${API_BASE_URL}/Asset/Get`,
    addAsset: `${API_BASE_URL}/Asset/Add`,
    editAsset: `${API_BASE_URL}/Asset/Edit`,
    deleteAsset: `${API_BASE_URL}/Asset/Delete`,
    getNewAssetNo: `${API_BASE_URL}/Asset/GetNewAssetNo`,
    validateExcelFile: `${API_BASE_URL}/Asset/ValidateExcelFile`,
    batchImport: `${API_BASE_URL}/Asset/BatchImport`,
    downloadBatchTemplate: `${API_BASE_URL}/Asset/DownloadBatchTemplate`,

    // AssetSource 財產來源管理
    getAssetSources: `${API_BASE_URL}/AssetSource/GetAll`,
    getAssetSourceDetail: `${API_BASE_URL}/AssetSource/Get`,
    addAssetSource: `${API_BASE_URL}/AssetSource/Add`,
    editAssetSource: `${API_BASE_URL}/AssetSource/Edit`,
    deleteAssetSource: `${API_BASE_URL}/AssetSource/Delete`,

    // DepreciationFormDetail 折舊紀錄管理
    getDepreciations: `${API_BASE_URL}/DepreciationFormDetail/GetAll`,
    getDepreciationDetail: `${API_BASE_URL}/DepreciationFormDetail/Get`,
    addDepreciation: `${API_BASE_URL}/DepreciationFormDetail/Add`,
    editDepreciation: `${API_BASE_URL}/DepreciationFormDetail/Edit`,
    deleteDepreciation: `${API_BASE_URL}/DepreciationFormDetail/Delete`,
    getDecliningBalanceRate: `${API_BASE_URL}/DepreciationFormDetail/DecliningBalanceRate`,

    // DepreciationForm 固定資產折舊單管理
    getDepreciationForms: `${API_BASE_URL}/DepreciationForm/GetAll`,
    getAllDepreciationForms: `${API_BASE_URL}/DepreciationForm/GetAll`,
    addDepreciationForm: `${API_BASE_URL}/DepreciationForm/Add`,
    editDepreciationForm: `${API_BASE_URL}/DepreciationForm/Edit`,
    deleteDepreciationForm: `${API_BASE_URL}/DepreciationForm/Delete`,
    getDepreciationFormDetail: `${API_BASE_URL}/DepreciationForm/detail`,
    checkDepreciationFormUsage: `${API_BASE_URL}/DepreciationForm/check`,

    // InsuranceUnit 保險單位資料管理
    getInsuranceUnits: `${API_BASE_URL}/InsuranceUnit/GetAll`,
    getInsuranceUnitDetail: `${API_BASE_URL}/InsuranceUnit/Get`,
    addInsuranceUnit: `${API_BASE_URL}/InsuranceUnit/Add`,
    editInsuranceUnit: `${API_BASE_URL}/InsuranceUnit/Edit`,
    deleteInsuranceUnit: `${API_BASE_URL}/InsuranceUnit/Delete`,

    // Manufacturer 製造商資料管理
    getManufacturers: `${API_BASE_URL}/Manufacturer/GetAll`,
    getManufacturerDetail: `${API_BASE_URL}/Manufacturer/Get`,
    addManufacturer: `${API_BASE_URL}/Manufacturer/Add`,
    editManufacturer: `${API_BASE_URL}/Manufacturer/Edit`,
    deleteManufacturer: `${API_BASE_URL}/Manufacturer/Delete`,

    // StorageLocation 存放地點資料管理
    getStorageLocations: `${API_BASE_URL}/StorageLocation/GetAll`,
    getStorageLocationDetail: `${API_BASE_URL}/StorageLocation/Get`,
    addStorageLocation: `${API_BASE_URL}/StorageLocation/Add`,
    editStorageLocation: `${API_BASE_URL}/StorageLocation/Edit`,
    deleteStorageLocation: `${API_BASE_URL}/StorageLocation/Delete`,
    checkStorageCapacity: `${API_BASE_URL}/StorageLocation/CheckStorageCapacity`,

    //AmortizationSource 攤提來源管理
    getAmortizationSources: `${API_BASE_URL}/AmortizationSource/GetAll`,
    getAmortizationSourceDetail: `${API_BASE_URL}/AmortizationSource/Get`,
    addAmortizationSource: `${API_BASE_URL}/AmortizationSource/Add`,
    editAmortizationSource: `${API_BASE_URL}/AmortizationSource/Edit`,
    deleteAmortizationSource: `${API_BASE_URL}/AmortizationSource/Delete`,

    // AssetSubAccount 財產子目管理
    getAssetSubAccounts: `${API_BASE_URL}/AssetSubAccount/GetAll`,
    getAssetSubAccountDetail: `${API_BASE_URL}/AssetSubAccount/Get`,
    addAssetSubAccount: `${API_BASE_URL}/AssetSubAccount/Add`,
    editAssetSubAccount: `${API_BASE_URL}/AssetSubAccount/Edit`,
    deleteAssetSubAccount: `${API_BASE_URL}/AssetSubAccount/Delete`,

    // AssetStatus 財產狀態管理
    getAssetStatuses: `${API_BASE_URL}/AssetStatus/GetAll`,
    getAssetStatusDetail: `${API_BASE_URL}/AssetStatus/Get`,
    addAssetStatus: `${API_BASE_URL}/AssetStatus/Add`,
    editAssetStatus: `${API_BASE_URL}/AssetStatus/Edit`,
    deleteAssetStatus: `${API_BASE_URL}/AssetStatus/Delete`,

    // EquipmentType 設備類型
    getEquipmentTypes: `${API_BASE_URL}/EquipmentType/GetAll`,
    getEquipmentTypeDetail: `${API_BASE_URL}/EquipmentType/Get`,
    addEquipmentType: `${API_BASE_URL}/EquipmentType/Add`,
    editEquipmentType: `${API_BASE_URL}/EquipmentType/Edit`,
    deleteEquipmentType: `${API_BASE_URL}/EquipmentType/Delete`,

    // PmsUserRole 財產系統使用者身分管理
    getPmsUserRoles: `${API_BASE_URL}/PmsUserRole/GetAll`,
    getPmsUserRoleDetail: `${API_BASE_URL}/PmsUserRole/Get`,
    addPmsUserRole: `${API_BASE_URL}/PmsUserRole/Add`,
    editPmsUserRole: `${API_BASE_URL}/PmsUserRole/Edit`,
    deletePmsUserRole: `${API_BASE_URL}/PmsUserRole/Delete`,
    getUserRoles: `${API_BASE_URL}/PmsUserRole/GetUserRoles`,
    assignRoleToUser: `${API_BASE_URL}/PmsUserRole/AssignRoleToUser`,
    removeRoleFromUser: `${API_BASE_URL}/PmsUserRole/RemoveRoleFromUser`,

    // PmsSystemParameter 系統參數設定
    getPmsSystemParameters: `${API_BASE_URL}/PmsSystemParameter/GetAll`,
    getPmsSystemParametersByType: `${API_BASE_URL}/PmsSystemParameter/GetByType`,
    getPmsSystemParameterDetail: `${API_BASE_URL}/PmsSystemParameter/Get`,
    addPmsSystemParameter: `${API_BASE_URL}/PmsSystemParameter/Add`,
    editPmsSystemParameter: `${API_BASE_URL}/PmsSystemParameter/Edit`,
    deletePmsSystemParameter: `${API_BASE_URL}/PmsSystemParameter/Delete`,
    getDepreciationMethods: `${API_BASE_URL}/PmsSystemParameter/GetDepreciationMethods`,
    setDefaultDepreciationMethod: `${API_BASE_URL}/PmsSystemParameter/SetDefaultDepreciationMethod`,
    getDecliningBalanceRates: `${API_BASE_URL}/PmsSystemParameter/GetDecliningBalanceRates`,
    setDecliningBalanceRate: `${API_BASE_URL}/PmsSystemParameter/SetDecliningBalanceRate`,
    getInitializationStatus: `${API_BASE_URL}/PmsSystemParameter/GetInitializationStatus`,
    setInitializationStatus: `${API_BASE_URL}/PmsSystemParameter/SetInitializationStatus`,

    /**
     * Pas 人事薪資管理
     * **/
    // Employee 員工主檔管理
    getEmployeeList: `${API_BASE_URL}/Employee/GetAll`,
    getEmployeeDetail: `${API_BASE_URL}/Employee/Get`,
    addEmployee: `${API_BASE_URL}/Employee/Add`,
    editEmployee: `${API_BASE_URL}/Employee/Edit`,
    completeEmployee: `${API_BASE_URL}/Employee/Complete`,
    deleteEmployee: `${API_BASE_URL}/Employee/Delete`,

    // Education 學歷資料管理
    getEducationList: `${API_BASE_URL}/Education/GetAll`,
    getEducationDetail: `${API_BASE_URL}/Education/Get`,
    addEducation: `${API_BASE_URL}/Education/Add`,
    editEducation: `${API_BASE_URL}/Education/Edit`,
    deleteEducation: `${API_BASE_URL}/Education/Delete`,

    // Train 教育訓練資料管理
    getTrainList: `${API_BASE_URL}/Train/GetAll`,
    getTrainDetail: `${API_BASE_URL}/Train/Get`,
    addTrain: `${API_BASE_URL}/Train/Add`,
    editTrain: `${API_BASE_URL}/Train/Edit`,
    deleteTrain: `${API_BASE_URL}/Train/Delete`,

    // Examination 考試資料管理
    getExaminationList: `${API_BASE_URL}/Examination/GetAll`,
    getExaminationDetail: `${API_BASE_URL}/Examination/Get`,
    addExamination: `${API_BASE_URL}/Examination/Add`,
    editExamination: `${API_BASE_URL}/Examination/Edit`,
    deleteExamination: `${API_BASE_URL}/Examination/Delete`,

    // Certification 檢覈資料管理
    getCertificationList: `${API_BASE_URL}/Certification/GetAll`,
    getCertificationDetail: `${API_BASE_URL}/Certification/Get`,
    addCertification: `${API_BASE_URL}/Certification/Add`,
    editCertification: `${API_BASE_URL}/Certification/Edit`,
    deleteCertification: `${API_BASE_URL}/Certification/Delete`,

    // Undergo 歷任經歷資料管理
    getUndergoList: `${API_BASE_URL}/Undergo/GetAll`,
    getUndergoDetail: `${API_BASE_URL}/Undergo/Get`,
    addUndergo: `${API_BASE_URL}/Undergo/Add`,
    editUndergo: `${API_BASE_URL}/Undergo/Edit`,
    deleteUndergo: `${API_BASE_URL}/Undergo/Delete`,

    // Ensure 保證書資料管理
    getEnsureList: `${API_BASE_URL}/Ensure/GetAll`,
    getEnsureDetail: `${API_BASE_URL}/Ensure/Get`,
    addEnsure: `${API_BASE_URL}/Ensure/Add`,
    editEnsure: `${API_BASE_URL}/Ensure/Edit`,
    deleteEnsure: `${API_BASE_URL}/Ensure/Delete`,

    // Suspend 留職停薪資料管理
    getSuspendList: `${API_BASE_URL}/Suspend/GetAll`,     // GET /Suspend/GetAll/{userId}
    getSuspendDetail: `${API_BASE_URL}/Suspend/Get`,      // GET /Suspend/Get/{uid}
    addSuspend: `${API_BASE_URL}/Suspend/Add`,            // POST /Suspend/Add
    editSuspend: `${API_BASE_URL}/Suspend/Edit`,          // POST /Suspend/Edit
    deleteSuspend: `${API_BASE_URL}/Suspend/Delete`,      // POST /Suspend/Delete（Body 傳 uid）

    // Salary 薪資主檔管理
    getSalaryDetail: `${API_BASE_URL}/Salary/Get`, // GET
    editSalary: `${API_BASE_URL}/Salary/Edit`,

    // Hensure 眷屬依附資料管理
    getHensureList: `${API_BASE_URL}/Hensure/GetAll`,
    getHensureDetail: `${API_BASE_URL}/Hensure/Get`,
    addHensure: `${API_BASE_URL}/Hensure/Add`,
    editHensure: `${API_BASE_URL}/Hensure/Edit`,
    deleteHensure: `${API_BASE_URL}/Hensure/Delete`,

    // Dependent 扶養資料管理
    getDependentList: `${API_BASE_URL}/Dependent/GetAll`,
    getDependentDetail: `${API_BASE_URL}/Dependent/Get`,
    addDependent: `${API_BASE_URL}/Dependent/Add`,
    editDependent: `${API_BASE_URL}/Dependent/Edit`,
    deleteDependent: `${API_BASE_URL}/Dependent/Delete`,

    // PerformancePointGroup 點數群組管理
    getPerformancePointGroupList: `${API_BASE_URL}/PerformancePointGroup/GetAll`,
    getPerformancePointGroupDetail: `${API_BASE_URL}/PerformancePointGroup/Get`,
    addPerformancePointGroup: `${API_BASE_URL}/PerformancePointGroup/Add`,
    editPerformancePointGroup: `${API_BASE_URL}/PerformancePointGroup/Edit`,
    deletePerformancePointGroup: `${API_BASE_URL}/PerformancePointGroup/Delete`,

    // PerformancePointType 點數類型管理
    getPerformancePointTypeList: `${API_BASE_URL}/PerformancePointType/GetByGroup`,
    getPerformancePointTypeDetail: `${API_BASE_URL}/PerformancePointType/Get`,
    addPerformancePointType: `${API_BASE_URL}/PerformancePointType/Add`,
    editPerformancePointType: `${API_BASE_URL}/PerformancePointType/Edit`,
    deletePerformancePointType: `${API_BASE_URL}/PerformancePointType/Delete`,
    getGroupTypeCascaderOptions: `${API_BASE_URL}/PerformancePointType/GetGroupTypeCascaderOptions`,

    // PerformancePointRecord 員工點數紀錄管理
    getPerformancePointRecordList: `${API_BASE_URL}/PerformancePointRecord/GetByUser`,
    getPerformancePointRecordDetail: `${API_BASE_URL}/PerformancePointRecord/Get`,
    addPerformancePointRecord: `${API_BASE_URL}/PerformancePointRecord/Add`,
    editPerformancePointRecord: `${API_BASE_URL}/PerformancePointRecord/Edit`,
    deletePerformancePointRecord: `${API_BASE_URL}/PerformancePointRecord/Delete`,
    getPerformancePointSummary: `${API_BASE_URL}/PerformancePointRecord/GetPerformancePointSummary`,

    // RegularSalaryItem 常態薪資項目管理
    getRegularSalaryItemList: `${API_BASE_URL}/RegularSalaryItem/GetAll`,
    getRegularSalaryItemDetail: `${API_BASE_URL}/RegularSalaryItem/Get`,
    addRegularSalaryItem: `${API_BASE_URL}/RegularSalaryItem/Add`,
    editRegularSalaryItem: `${API_BASE_URL}/RegularSalaryItem/Edit`,
    deleteRegularSalaryItem: `${API_BASE_URL}/RegularSalaryItem/Delete`,
    getSalaryItemTypeOptions: `${API_BASE_URL}/RegularSalaryItem/GetSalaryItemTypeOptions`,

    // EmployeeRegularSalary 員工常態薪資管理
    getEmployeeRegularSalaryList: `${API_BASE_URL}/EmployeeRegularSalary/GetByUser`,
    getEmployeeRegularSalaryDetail: `${API_BASE_URL}/EmployeeRegularSalary/Get`,
    addEmployeeRegularSalary: `${API_BASE_URL}/EmployeeRegularSalary/Add`,
    editEmployeeRegularSalary: `${API_BASE_URL}/EmployeeRegularSalary/Edit`,
    deleteEmployeeRegularSalary: `${API_BASE_URL}/EmployeeRegularSalary/Delete`,

    // SalaryPoint 薪點資料管理
    getSalaryPointList: `${API_BASE_URL}/SalaryPoint/GetAll`,
    getSalaryPointDetail: `${API_BASE_URL}/SalaryPoint/Get`,
    addSalaryPoint: `${API_BASE_URL}/SalaryPoint/Add`,
    editSalaryPoint: `${API_BASE_URL}/SalaryPoint/Edit`,
    deleteSalaryPoint: `${API_BASE_URL}/SalaryPoint/Delete`,
    getSalaryPointAmountByDate: `${API_BASE_URL}/SalaryPoint/GetAmountByDate`,

    // DepartmentSalaryPoint 部門薪點資料管理
    getDepartmentSalaryPointList: `${API_BASE_URL}/DepartmentSalaryPoint/GetAll`,
    getDepartmentSalaryPointListByDepartment: `${API_BASE_URL}/DepartmentSalaryPoint/GetByDepartment`,
    getDepartmentSalaryPointDetail: `${API_BASE_URL}/DepartmentSalaryPoint/Get`,
    addDepartmentSalaryPoint: `${API_BASE_URL}/DepartmentSalaryPoint/Add`,
    editDepartmentSalaryPoint: `${API_BASE_URL}/DepartmentSalaryPoint/Edit`,
    deleteDepartmentSalaryPoint: `${API_BASE_URL}/DepartmentSalaryPoint/Delete`,
    getDepartmentSalaryPointAmountByDate: `${API_BASE_URL}/DepartmentSalaryPoint/GetAmountByDate`,

    // InsuranceGrade 保險級距管理
    getInsuranceGradeList: `${API_BASE_URL}/InsuranceGrade/GetInsuranceGradeList`,
    getInsuranceGradeDetail: `${API_BASE_URL}/InsuranceGrade/GetInsuranceGradeDetail`,
    addInsuranceGrade: `${API_BASE_URL}/InsuranceGrade/Add`,
    editInsuranceGrade: `${API_BASE_URL}/InsuranceGrade/Edit`,
    deleteInsuranceGrade: `${API_BASE_URL}/InsuranceGrade/Delete`,
    getInsuranceGradeBySalary: `${API_BASE_URL}/InsuranceGrade/by-salary`,
    getEmployeeInsuranceGradeDetail: `${API_BASE_URL}/InsuranceGrade/GetEmployeeInsuranceGradeDetail`,

    // InsuranceHistory 保險級距歷程管理
    getInsuranceHistoryDetail: `${API_BASE_URL}/InsuranceHistory/Get`,
    getInsuranceHistoryByUserAndType: `${API_BASE_URL}/InsuranceHistory/GetByUserAndType`,
    getEffectiveInsuranceGrade: `${API_BASE_URL}/InsuranceHistory/GetEffectiveGrade`,
    getAllEffectiveInsuranceGrades: `${API_BASE_URL}/InsuranceHistory/GetAllEffectiveGrades`,

    addInsuranceHistory: `${API_BASE_URL}/InsuranceHistory/Add`,
    editInsuranceHistory: `${API_BASE_URL}/InsuranceHistory/Edit`,
    deleteInsuranceHistory: `${API_BASE_URL}/InsuranceHistory/Delete`,

    // PasOptionParameter 參數選單設定
    getJobtitleOptions: `${API_BASE_URL}/PasOptionParameter/GetJobtitleOptions`, // 取得職稱選項.
    getIdTypeOptions: `${API_BASE_URL}/PasOptionParameter/GetIdTypeOptions`, // 取得證號別選項.
    getIdErrorOptions: `${API_BASE_URL}/PasOptionParameter/GetIdErrorOptions`, // 取得證號錯誤註記選項.
    getBloodTypeOptions: `${API_BASE_URL}/PasOptionParameter/GetBloodTypeOptions`, // 取得血型選項.
    getDegreeTypeOptions: `${API_BASE_URL}/PasOptionParameter/GetDegreeTypeOptions`, // 取得學位類型選項.
    getGraduateOptions: `${API_BASE_URL}/PasOptionParameter/GetGraduateOptions`, // 取得結業類型選項.
    getSuspendTypeOptions: `${API_BASE_URL}/PasOptionParameter/GetSuspendTypeOptions`, // 取得留停類型選項.
    getSuspendKindOptions: `${API_BASE_URL}/PasOptionParameter/GetSuspendKindOptions`, // 取得留停種類選項.
    getEmployeeContributionOptions: `${API_BASE_URL}/PasOptionParameter/GetEmployeeContributionOptions`, // 取得員工自提額類型選項.
    getIncomeTaxTypeOptions: `${API_BASE_URL}/PasOptionParameter/GetIncomeTaxTypeOptions`, // 取得計稅型式類型選項.
    getPayoffTypeOptions: `${API_BASE_URL}/PasOptionParameter/GetPayoffTypeOptions`, // 取得發薪狀況選項.
    getDepTypeOptions: `${API_BASE_URL}/PasOptionParameter/GetDepTypeOptions`, // 取得稱謂關係選項.
    getRegularSalaryCreaseTypeOptions: `${API_BASE_URL}/PasOptionParameter/GetRegularSalaryCreaseTypeOptions`, // 取得加減薪項目類型選項.
    getJobroleTypeOptions: `${API_BASE_URL}/PasOptionParameter/GetJobroleTypeOptions`, // 取得任用資格選項.
    getSalaryTypeOptions: `${API_BASE_URL}/PasOptionParameter/GetSalaryTypeOptions`, // 取得薪俸類型選項.
    getCategoryTypeOptions: `${API_BASE_URL}/PasOptionParameter/GetCategoryTypeOptions`, // 取得錄用類別選項.
    getJobLevelOptions: `${API_BASE_URL}/PasOptionParameter/GetJobLevelOptions`, // 取得職等選項.
    getJobRankOptions: `${API_BASE_URL}/PasOptionParameter/GetJobRankOptions`, // 取得級數選項.
    getPromotionTypeOptions: `${API_BASE_URL}/PasOptionParameter/GetPromotionTypeOptions`, // 取得升遷類型選項.


    // VendorMaintenance 廠商修繕單管理
    getVendorMaintenances: `${API_BASE_URL}/VendorMaintenance/GetAll`,
    getVendorMaintenanceById: `${API_BASE_URL}/VendorMaintenance`,
    addVendorMaintenance: `${API_BASE_URL}/VendorMaintenance/Add`,
    editVendorMaintenance: `${API_BASE_URL}/VendorMaintenance/Edit`,
    deleteVendorMaintenance: `${API_BASE_URL}/VendorMaintenance/Delete`,
    approveVendorMaintenance: `${API_BASE_URL}/VendorMaintenance`,
    assignVendor: `${API_BASE_URL}/VendorMaintenance`,
    startWork: `${API_BASE_URL}/VendorMaintenance`,
    completeMaintenance: `${API_BASE_URL}/VendorMaintenance`,
    inspectMaintenance: `${API_BASE_URL}/VendorMaintenance`,
    closeMaintenance: `${API_BASE_URL}/VendorMaintenance`,
    cancelMaintenance: `${API_BASE_URL}/VendorMaintenance`,
    batchProcessMaintenance: `${API_BASE_URL}/VendorMaintenance/batch`,
    getMaintenanceStatistics: `${API_BASE_URL}/VendorMaintenance/statistics`,
    getOverdueMaintenance: `${API_BASE_URL}/VendorMaintenance/overdue`,

    // AssetCarryOut 資產攜出申請管理
    getAssetCarryOuts: `${API_BASE_URL}/AssetCarryOut/GetAll`,
    getAssetCarryOutById: `${API_BASE_URL}/AssetCarryOut`,
    addAssetCarryOut: `${API_BASE_URL}/AssetCarryOut/Add`,
    editAssetCarryOut: `${API_BASE_URL}/AssetCarryOut`,
    deleteAssetCarryOut: `${API_BASE_URL}/AssetCarryOut/Delete`,
    approveAssetCarryOut: `${API_BASE_URL}/AssetCarryOut`,
    registerCarryOut: `${API_BASE_URL}/AssetCarryOut`,
    registerReturn: `${API_BASE_URL}/AssetCarryOut`,
    batchProcessCarryOut: `${API_BASE_URL}/AssetCarryOut/batch`,
    getCarryOutStatistics: `${API_BASE_URL}/AssetCarryOut/statistics`,
    getOverdueCarryOuts: `${API_BASE_URL}/AssetCarryOut/overdue`,
    generateCarryOutNumber: `${API_BASE_URL}/AssetCarryOut/generate-number`,

    // #region Ims進銷存管理系統
    // Item 庫存品管理
    getItemList: `${API_BASE_URL}/Item/GetAll`,     //取得庫存品列表
    getItem: `${API_BASE_URL}/Item`,                //取得庫存品 (GET)
    addItem: `${API_BASE_URL}/Item`,                //新增庫存品 (POST)
    editItem: `${API_BASE_URL}/Item`,               //修改庫存品 (PATCH)
    deleteItem: `${API_BASE_URL}/Item`,             //刪除庫存品 (DELETE)
    generateTestItems: `${API_BASE_URL}/Item/GenerateTestData`, //產生測試資料 (POST) - 僅開發環境
    getItemTaxTypes: `${API_BASE_URL}/Item/GetItemTaxTypes`, //取得庫存品稅別選項 (GET)

    // ItemCategory 庫存品分類管理
    getItemCategoryList: `${API_BASE_URL}/ItemCategory/GetAll`,     //取得庫存品分類列表
    getItemCategory: `${API_BASE_URL}/ItemCategory`,                //取得庫存品分類
    addItemCategory: `${API_BASE_URL}/ItemCategory`,                //新增庫存品分類
    editItemCategory: `${API_BASE_URL}/ItemCategory`,               //修改庫存品分類
    deleteItemCategory: `${API_BASE_URL}/ItemCategory`,             //刪除庫存品分類

    // ItemPrice 庫存品價格管理
    getItemPriceList: `${API_BASE_URL}/ItemPrice/GetAll`,     //取得庫存品價格列表
    getItemPrice: `${API_BASE_URL}/ItemPrice`,                //取得庫存品價格
    addItemPrice: `${API_BASE_URL}/ItemPrice`,                //新增庫存品價格
    editItemPrice: `${API_BASE_URL}/ItemPrice`,               //修改庫存品價格
    deleteItemPrice: `${API_BASE_URL}/ItemPrice`,             //刪除庫存品價格

    // PriceType 價格類型管理
    getPriceTypeList: `${API_BASE_URL}/PriceType/GetAll`,     //取得價格類型列表
    getPriceType: `${API_BASE_URL}/PriceType`,                //取得價格類型
    addPriceType: `${API_BASE_URL}/PriceType`,                //新增價格類型
    editPriceType: `${API_BASE_URL}/PriceType`,               //修改價格類型
    deletePriceType: `${API_BASE_URL}/PriceType`,             //刪除價格類型

    // Partner 商業夥伴管理
    getPartnerList: `${API_BASE_URL}/Partner`,          //取得商業夥伴列表
    getPartner: `${API_BASE_URL}/Partner`,              //取得商業夥伴
    addPartner: `${API_BASE_URL}/Partner`,              //新增商業夥伴
    editPartner: `${API_BASE_URL}/Partner`,             //修改商業夥伴
    deletePartner: `${API_BASE_URL}/Partner`,           //刪除商業夥伴

    // CustomerCategory 客戶分類管理
    getCustomerCategoryList: `${API_BASE_URL}/CustomerCategory/GetAll`,     //取得客戶分類列表
    getCustomerCategory: `${API_BASE_URL}/CustomerCategory`,                //取得客戶分類
    addCustomerCategory: `${API_BASE_URL}/CustomerCategory`,                //新增客戶分類
    editCustomerCategory: `${API_BASE_URL}/CustomerCategory`,               //修改客戶分類
    deleteCustomerCategory: `${API_BASE_URL}/CustomerCategory`,             //刪除客戶分類

    // SupplierCategory 供應商分類管理
    getSupplierCategoryList: `${API_BASE_URL}/SupplierCategory/GetAll`,     //取得供應商分類列表
    getSupplierCategory: `${API_BASE_URL}/SupplierCategory`,                //取得供應商分類
    addSupplierCategory: `${API_BASE_URL}/SupplierCategory`,                //新增供應商分類
    editSupplierCategory: `${API_BASE_URL}/SupplierCategory`,               //修改供應商分類
    deleteSupplierCategory: `${API_BASE_URL}/SupplierCategory`              //刪除供應商分類
    // #endregion
} as const;

// API 請求配置
export const apiConfig = {
    baseURL: API_BASE_URL,
    // 不設定 header，讓 axios 自動決定
} as const;

// API 響應介面
export interface ApiResponse<T = any> {
    success: boolean;
    message?: string;
    data?: T;
}