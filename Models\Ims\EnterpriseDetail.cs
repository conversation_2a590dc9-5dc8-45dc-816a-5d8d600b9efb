using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FAST_ERP_Backend.Attributes;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Ims;

/// <summary> 法人 </summary>
public class EnterpriseDetail
{
    /// <summary> 商業夥伴編號 </summary>
    [Key]
    [Comment("商業夥伴編號")]
    [Column(TypeName = "nvarchar(100)")]
    public Guid PartnerID { get; set; }

    /// <summary> 公司名稱 </summary>
    [Comment("公司名稱")]
    [Column(TypeName = "nvarchar(100)")]
    public string? CompanyName { get; set; }

    /// <summary> 統一編號 </summary>
    [Comment("統一編號")]
    [Column(TypeName = "nvarchar(20)")]
    public string? BussinessID { get; set; }
    
    /// <summary> 稅籍編號 </summary>
    [Comment("稅籍編號")]
    [Column(TypeName = "nvarchar(20)")]
    public string? TaxID { get; set; }

    /// <summary> 負責人 </summary>
    [Comment("負責人")]
    [Column(TypeName = "nvarchar(20)")]
    public string? ResponsiblePerson { get; set; }
    
    /// <summary> 建構子 </summary>
    public EnterpriseDetail()
    {
        PartnerID = Guid.Empty;
    }
}

/// <summary> 法人DTO </summary>
public class EnterpriseDetailDTO
{
    /// <summary> 商業夥伴編號 </summary>
    public Guid PartnerID { get; set; }

    /// <summary> 公司名稱 </summary>
    public string? CompanyName { get; set; }

    /// <summary> 統一編號 </summary>
    public string? BussinessID { get; set; }
    
    /// <summary> 稅籍編號 </summary>
    public string? TaxID { get; set; }

    /// <summary> 負責人 </summary>
    public string? ResponsiblePerson { get; set; }
    
    /// <summary> 建構子 </summary>
    public EnterpriseDetailDTO()
    {
        PartnerID = Guid.Empty;
    }
}
