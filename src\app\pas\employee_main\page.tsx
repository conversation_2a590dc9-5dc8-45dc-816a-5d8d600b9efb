'use client';

import React, { useEffect, useState } from 'react';
import { Employee, getEmployeeList, FilterData, getEmployeeDetail, deleteEmployee } from '@/services/pas/EmployeeService';
import { Input, Button, Space, message, Card, Select, Modal, Badge, Row, Col, Avatar, Popconfirm, Drawer, Tabs } from 'antd';
import { LeftOutlined, RightOutlined, MenuOutlined, UserAddOutlined, EditOutlined, DeleteOutlined, ReloadOutlined } from '@ant-design/icons';

// Material Icons CSS
import '@/styles/material-icons.css';

// component.
import FilterTypeSelect from '@/app/pas/components/FilterTypeSelect'; // 搜尋框
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import TabsCard from '@/app/pas/employee_main/taboptions/TabsCard'; // 功能選項
import { allTabs } from '@/app/pas/employee_main/taboptions/constants/tabOptions'; // 選單項目


// employee_modules_page.
import PromotionInfo from '@/app/pas/employee_main/employee_modules/promotion/PromotionInfo'; // 升遷資料
import EducationInfo from '@/app/pas/employee_main/employee_modules/education/EducationInfo'; // 學歷資料
import TrainInfo from '@/app/pas/employee_main/employee_modules/train/TrainInfo'; // 教育訓練資料
import ExaminationInfo from '@/app/pas/employee_main/employee_modules/examination/ExaminationInfo'; // 考試資料
import CertificationInfo from '@/app/pas/employee_main/employee_modules/certification/CertificationInfo'; // 檢覈資料
import UndergoInfo from '@/app/pas/employee_main/employee_modules/undergo/UndergoInfo'; // 經歷資料
import EnsureInfo from '@/app/pas/employee_main/employee_modules/ensure/EnsureInfo'; // 保證資料
import SuspendInfo from '@/app/pas/employee_main/employee_modules/suspend/SuspendInfo'; // 留停資料
import HensureInfo from '@/app/pas/employee_main/employee_modules/hensure/HensureInfo'; // 眷保資料
import DependentInfo from '@/app/pas/employee_main/employee_modules/dependent/DependentInfo'; // 扶養資料
import PerformancePointRecordInfo from '@/app/pas/employee_main/employee_modules/performancePointRecord/PerformancePointRecordInfo' //績效點數紀錄資料
import RegularSalaryRecordInfo from '@/app/pas/employee_main/employee_modules/regularSalaryRecord/RegularSalaryRecordInfo'; // 常態性薪資管理
import InsuranceHistoryInfo from './employee_modules/insuranceHistory/InsuranceHistoryInfo';
import ExpenseDepartmentChangeInfo from '@/app/pas/employee_main/employee_modules/expenseDepartment/ExpenseDepartmentChangeInfo'; // 開支部門異動
import ServiceDepartmentChangeInfo from '@/app/pas/employee_main/employee_modules/serviceDepartment/ServiceDepartmentChangeInfo'; // 服務部門異動

import SalaryInfo from '@/app/pas/employee_main/employee_modules/salary/SalaryInfo'; // 薪資主檔資料
import EmployeeInfo from '@/app/pas/employee_main/employee_modules/employee/EmployeeInfo'; // 人事主檔資料
import EditEmployeeForm from '@/app/pas/employee_main/employee_modules/employee/EditEmployeeForm'; // 編輯人事資料頁面

import './styles/employeePage.css';

// Loading 組件
const LoadingSpinner = ({ size = 'default', text = '' }: { size?: 'small' | 'default' | 'large'; text?: string }) => (
  <div className="loading-container">
    <div className={size === 'small' ? 'loading-spinner-small' : 'loading-spinner'} />
    {text && <div className={size === 'large' ? 'loading-text-large' : 'loading-text'}>{text}</div>}
  </div>
);

// 全頁面 Loading 覆蓋層
const PageLoadingOverlay = ({ text = '載入中...' }: { text?: string }) => (
  <div className="loading-overlay">
    <LoadingSpinner size="large" text={text} />
  </div>
);

// 區域 Loading 覆蓋層
const SectionLoadingOverlay = ({ text = '載入中...' }: { text?: string }) => (
  <div className="section-loading-overlay">
    <LoadingSpinner text={text} />
  </div>
);

// 骨架屏組件
const SkeletonCard = () => (
  <div className="skeleton-card" style={{ height: '200px', padding: '20px' }}>
    <div className="skeleton-line long" />
    <div className="skeleton-line medium" />
    <div className="skeleton-line short" />
    <div className="skeleton-line long" />
  </div>
);

const EmployeePage = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<string>(''); // 當前指到的userid
  const [deleteUserid, setDeleteUserid] = useState<string | null>(null);
  const [page, setPage] = useState(0); // 第幾頁（0開始）
  const [currentIndex, setCurrentIndex] = useState(0); // 目前在第幾筆 (for單筆切換)
  const [viewMode, setViewMode] = useState<'card' | 'single'>('card'); // 顯示模式：多張/單筆

  // Loading 狀態管理
  const [loadingStates, setLoadingStates] = useState({
    pageLoading: true,        // 整頁loading，初始設為 true
    employeeList: false,       // 員工列表loading
    employeeDetail: false,     // 員工明細loading
    refreshing: false,         // 重新載入loading
    deleting: false,           // 刪除loading
    searching: false,          // 搜尋loading
  });

  const pageSize = 6; // 一頁6筆
  const total = employees.length; // 總筆數
  const totalPages = Math.ceil(total / pageSize); // 總頁數

  const [employeeDetail, setEmployeeDetail] = useState<Employee | null>(null); //當筆員工資料明細.
  const [isModalVisible, setIsModalVisible] = useState(false); // 功能選單modal顯示狀態
  const [editModalVisible, setEditModalVisible] = useState(false); //人事資料用modal顯示狀態
  const [editStatus, setEditStatus] = useState<string>('') //人事資料用modal模式
  const [expenseDepartmentModalVisible, setExpenseDepartmentModalVisible] = useState(false); // 開支部門異動modal顯示狀態
  const [serviceDepartmentModalVisible, setServiceDepartmentModalVisible] = useState(false); // 服務部門異動modal顯示狀態
  const [filterData, setFilterData] = useState<FilterData>({ //篩選條件
    filterType: 'EmpNo',
    filterValue: '',
  });
  const [selectedTab, setSelectedTab] = useState<string>('employee'); // 當前選取的功能選單
  const [tabUpdateIdx, setTabUpdateIdx] = useState<number>(0); // 更新flag.

  // 更新loading狀態的輔助函數
  const updateLoadingState = (key: keyof typeof loadingStates, value: boolean) => {
    setLoadingStates(prev => ({ ...prev, [key]: value }));
  };

  useEffect(() => {
    updateLoadingState('pageLoading', true);
    fetchEmployees().finally(() => {
      updateLoadingState('pageLoading', false);
    }); // 初始撈全部使用者ID
  }, []);

  // 鍵盤事件監聽器 - 方向鍵上下控制上一筆下一筆
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 檢查是否在輸入框中，如果是則不處理鍵盤事件
      const activeElement = document.activeElement;
      const isInputFocused = activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.tagName === 'SELECT' ||
        activeElement.getAttribute('contenteditable') === 'true'
      );

      if (isInputFocused) return;

      // 處理方向鍵
      if (event.key === 'ArrowUp') {
        event.preventDefault();
        handlePrevEmployee();
      } else if (event.key === 'ArrowDown') {
        event.preventDefault();
        handleNextEmployee();
      }
    };

    // 添加事件監聽器
    window.addEventListener('keydown', handleKeyDown);

    // 清理函數 - 移除事件監聽器
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentIndex, total]); // 依賴於當前索引和總數

  // 依輸入條件搜尋user清單.
  const handleSearch = () => {
    updateLoadingState('searching', true);
    fetchEmployees().finally(() => {
      updateLoadingState('searching', false);
    });
  };

  const fetchEmployees = async () => {
    try {
      updateLoadingState('employeeList', true);
      const response = await getEmployeeList(filterData);
      if (response.success && Array.isArray(response.data)) {
        setEmployees(response.data);
        if (response.data.length > 0) {
          setCurrentIndex(0); // 當前第幾筆.
          setPage(0); // 重新搜尋返回第1頁
          setSelectedUserId(response.data[0].usersDTO.userId);
        } else {
          setEmployees([]); // 把員工清單清空（防止殘留）
          setEmployeeDetail(null);
          setCurrentIndex(-1); // 當前第幾筆.
          setPage(0); // 沒資料時頁碼也回到0
          setSelectedUserId('');
          message.info('查無資料');
        }
      } else {
        message.error(response.message || '取得員工列表失敗');
      }
    } catch (error) {
      message.error('伺服器錯誤');
    } finally {
      updateLoadingState('employeeList', false);
    }
  };

  // 明細獲取更新
  useEffect(() => {
    fetchEmployeeDetail(selectedUserId);
  }, [selectedUserId]);

  // 依目前userid搜尋員工明細
  const fetchEmployeeDetail = async (userid: string) => {
    if (userid === '') {
      return;
    }

    try {
      updateLoadingState('employeeDetail', true);
      const response = await getEmployeeDetail(userid);
      if (response.success) {
        if (response.data) {
          setEmployeeDetail(response.data);
        } else {
          setEmployeeDetail(null);
          message.info('查無資料');
        }
      } else {
        message.error(response.message || '取得員工明細資料失敗');
      }

    } catch (error) {
      console.error('取得員工明細失敗', error);
      message.error('載入員工明細失敗');
    } finally {
      updateLoadingState('employeeDetail', false);
    }
  };



  // 功能選單控制
  useEffect(() => {
    setIsModalVisible(false);
  }, [selectedTab]);

  const handleAddClick = () => {
    setEditModalVisible(true);
    setEditStatus('add');
  };

  const handleEditClick = () => {
    setEditModalVisible(true);
    setEditStatus('edit');
  };

  const handlecompleteClick = () => {
    setEditStatus('complete');
    setEditModalVisible(true);
  };

  const deleteEmployeeDetail = async (userid: string) => {
    if (userid === '') return;

    try {
      updateLoadingState('deleting', true);
      const response = await deleteEmployee(userid);
      if (response.data.result) {
        message.success('刪除完成');

        // Step 1: 找出目前 index
        const currentIdx = employees.findIndex(emp => emp.usersDTO.userId === userid);
        let newSelectedUserId = '';
        let newIndex = -1;

        // Step 2: 刪除成功後選擇的下一筆
        if (employees.length > 1) {
          if (currentIdx > 0) {
            // 有上一筆，選上一筆
            newIndex = currentIdx - 1;
          } else {
            // 沒有上一筆，選下一筆
            newIndex = 1;
          }
          newSelectedUserId = employees[newIndex].usersDTO.userId;
        }

        // Step 3: 重新撈資料
        const result = await getEmployeeList(filterData);
        if (result.success && Array.isArray(result.data)) {
          setEmployees(result.data);

          if (newSelectedUserId) {
            setSelectedUserId(newSelectedUserId);
            setCurrentIndex(newIndex);
            setPage(Math.floor(newIndex / pageSize));
          } else {
            // 如果刪完沒有資料
            setSelectedUserId('');
            setCurrentIndex(-1);
            setPage(0);
            setEmployeeDetail(null);
          }
        }

        setTabUpdateIdx(prev => prev + 1);

      } else {
        message.error(response.data.msg || '刪除員工資料失敗');
      }

    } catch (error) {
      console.error('刪除員工資料失敗', error);
      message.error('刪除員工資料失敗');
    } finally {
      updateLoadingState('deleting', false);
    }
  };



  const handleEditSuccess = () => {
    setEditModalVisible(false);
    updateEmployees(); // 異動成功後重新撈資料
    setTabUpdateIdx(prev => prev + 1);
  };

  const handleEditCancel = () => {
    setEditModalVisible(false);
    setEditStatus('');
  };

  // 開支部門異動Modal處理函數
  const handleExpenseDepartmentClick = () => {
    if (selectedUserId) {
      setExpenseDepartmentModalVisible(true);
    }
  };

  const handleExpenseDepartmentModalClose = () => {
    setExpenseDepartmentModalVisible(false);
  };

  // 服務部門異動Modal處理函數
  const handleServiceDepartmentClick = () => {
    if (selectedUserId) {
      setServiceDepartmentModalVisible(true);
    }
  };

  const handleServiceDepartmentModalClose = () => {
    setServiceDepartmentModalVisible(false);
  };

  // 重新載入當前員工資料
  const handleRefresh = async () => {
    if (selectedUserId) {
      try {
        updateLoadingState('refreshing', true);
        await Promise.all([
          fetchEmployeeDetail(selectedUserId),
          updateEmployees()
        ]);
        setTabUpdateIdx(prev => prev + 1);
        message.success('資料重新載入完成');
      } catch (error) {
        message.error('重新載入失敗');
      } finally {
        updateLoadingState('refreshing', false);
      }
    }
  };

  //更新員工列表資料 (delete update add)
  const updateEmployees = async () => {
    const response = await getEmployeeList(filterData);

    if (response.success && Array.isArray(response.data)) {
      setEmployees(response.data);
      fetchEmployeeDetail(selectedUserId);
    } else {
      message.error(response.message || '更新員工列表失敗');
    }
  };

  /* 員工資料卡檢視控制 */
  const currentEmployees = employees.slice(page * pageSize, page * pageSize + pageSize);

  // 切上一筆（單筆）
  const handlePrevEmployee = () => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      setCurrentIndex(newIndex);
      setSelectedUserId(employees[newIndex].usersDTO.userId);

      const newPage = Math.floor(newIndex / pageSize);
      if (newPage !== page) setPage(newPage);
    }
  };

  // 切下一筆（單筆）
  const handleNextEmployee = () => {
    if (currentIndex < total - 1) {
      const newIndex = currentIndex + 1;
      setCurrentIndex(newIndex);
      setSelectedUserId(employees[newIndex].usersDTO.userId);

      const newPage = Math.floor(newIndex / pageSize);
      if (newPage !== page) setPage(newPage);
    }
  };

  return (
    <div className="employee-page-container">
      {/* 頁面載入 Loading 覆蓋層 */}
      {loadingStates.pageLoading && <PageLoadingOverlay text="正在載入員工資料..." />}

      <h2 className="employee-page-title">員工資料主頁</h2>
      <div style={{ display: 'flex', alignItems: 'center', gap: 16, marginBottom: 16 }}>
        <Space wrap>
          <FilterTypeSelect
            filterType={filterData.filterType}
            filterValue={filterData.filterValue}
            onFilterChange={(field, value) => setFilterData(prev => ({ ...prev, [field]: value }))}
            onSearch={handleSearch}
          />
          {loadingStates.searching && (
            <div className="dots-loading">
              <span></span>
              <span></span>
              <span></span>
            </div>
          )}
        </Space>

        <div>
          <span>總筆數: {total}</span>
          <Button disabled={currentIndex <= 0} onClick={handlePrevEmployee} style={{ marginLeft: 8 }}>
            上一筆
          </Button>
          <Button disabled={currentIndex >= total - 1} onClick={handleNextEmployee} style={{ marginLeft: 8 }}>
            下一筆
          </Button>
          <Button onClick={() => setViewMode(viewMode === 'card' ? 'single' : 'card')} style={{ marginLeft: 8 }}>
            {viewMode === 'card' ? '切到單筆' : '切到名片'}
          </Button>
        </div>
      </div>



      <Card>
        <Row gutter={16}>
          {/* 左邊名片區 */}
          <Col xs={24} sm={24} md={24} lg={18} xl={18} style={{ transition: 'all 0.3s ease' }}>
            {loadingStates.employeeList ? (
              // 載入中顯示骨架屏
              <Card>
                <div style={{ paddingTop: '40px' }}>
                  <Row gutter={[16, 16]}>
                    {Array.from({ length: pageSize }).map((_, index) => (
                      <Col key={index} sm={24} md={12} xl={8}>
                        <SkeletonCard />
                      </Col>
                    ))}
                  </Row>
                </div>
              </Card>
            ) : employees.length === 0 ? (
              <div className="empty-state">
                <span className="empty-state-title">查無員工資料</span>
              </div>
            ) : (
              <>
                {viewMode === 'card' ? (
                  // 名片模式
                  <Card style={{ position: 'relative' }}>
                    {loadingStates.employeeDetail && <SectionLoadingOverlay text="載入員工明細..." />}
                    <div style={{ paddingTop: '40px' }}>
                      <Row gutter={[16, 16]}>
                        {currentEmployees.map((emp) => {
                          return (
                            <Col key={emp.usersDTO.userId} sm={24} md={12} xl={8}>
                              <Card
                                hoverable
                                className={`employee-card ${selectedUserId === emp.usersDTO.userId ? 'employee-card-selected' : 'employee-card-normal'} ${deleteUserid === emp.usersDTO.userId ? 'card-deleting' : ''}`}

                                onClick={() => {
                                  setSelectedUserId(emp.usersDTO.userId);
                                  const newIndex = employees.findIndex(e => e.usersDTO.userId === emp.usersDTO.userId);
                                  if (newIndex !== -1) {
                                    setCurrentIndex(newIndex);
                                  }
                                  if (emp.userId === "") {
                                    Modal.confirm({
                                      title: '資料不完整',
                                      content: '此員工資料尚未補全，是否立即補全？',
                                      okText: '是',
                                      cancelText: '否',
                                      onOk() {
                                        handlecompleteClick();
                                      },
                                      onCancel() {
                                      }
                                    });
                                  }
                                }}
                              >
                                <div className="employee-card-body">
                                  <div className="employee-card-header">
                                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
                                      <span className="employee-name-tag">姓名</span>
                                      <h3 className="employee-name">
                                        {emp.usersDTO.name}
                                        {selectedUserId === emp.usersDTO.userId && (
                                          <span className="selected-badge">已選取</span>
                                        )}
                                      </h3>
                                    </div>
                                  </div>

                                  <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                    <div className="employee-info-row">
                                      <span className="info-label">員工編號</span>
                                      <span className="info-value">{emp.empNo}</span>
                                    </div>

                                    <div className="employee-info-row">
                                      <span className="info-label">身份證號</span>
                                      <span className="info-value">{emp.idNo}</span>
                                    </div>
                                  </div>

                                  {emp.userId === "" && (
                                    <div className="incomplete-warning">
                                      <span className="warning-dot"></span>
                                      <span className="warning-text">員工資料未建立</span>
                                    </div>
                                  )}
                                </div>
                              </Card>
                            </Col>
                          );
                        })}
                      </Row>
                    </div>

                    <div className="pagination-controls">
                      <Button
                        type="primary"
                        shape="circle"
                        icon={<LeftOutlined />}
                        disabled={page === 0}
                        onClick={() => setPage(page - 1)}
                      />
                      <span className="page-number">
                        {page + 1} / {totalPages}
                      </span>
                      <Button
                        type="primary"
                        shape="circle"
                        icon={<RightOutlined />}
                        disabled={page >= totalPages - 1}
                        onClick={() => setPage(page + 1)}
                      />
                    </div>
                  </Card>
                ) : (
                  // 單筆詳細資料模式
                  <Card
                    title={
                      <div className="detail-card-title">
                        <span className="material-icons">person</span>
                        詳細資料
                      </div>
                    }
                    className="detail-main-card"
                    style={{ position: 'relative' }}
                  >
                    {loadingStates.employeeDetail && <SectionLoadingOverlay text="載入員工明細..." />}
                    {selectedUserId ? (
                      (() => {
                        const selectedEmp = employees.find(emp => emp.userId === selectedUserId); //這邊搜尋用selectid 對 emp userid 沒有代表沒有建立employee資料.
                        return selectedEmp ? (
                          <div className="detail-content-wrapper">
                            <Row gutter={[24, 24]}>
                              {/* 人事資料 */}
                              <Col xs={24} lg={16}>
                                <Card
                                  title={
                                    <div className="personnel-info-title">
                                      <span className="material-icons">badge</span>
                                      人事資料
                                    </div>
                                  }
                                  className="personnel-info-card"
                                  bodyStyle={{ padding: '20px' }}
                                >
                                  <Row gutter={[16, 16]}>
                                    <Col xs={24} md={12}>
                                      <div className="personnel-info-column">
                                        <div className="detail-data-item">
                                          <span className="detail-data-label detail-data-label-wide">員工編號：</span>
                                          <span className="detail-data-value">{selectedEmp?.empNo ?? ''}</span>
                                        </div>
                                        <div className="detail-data-item">
                                          <span className="detail-data-label detail-data-label-wide">身份證字號：</span>
                                          <span className="detail-data-value">{selectedEmp?.idNo ?? ''}</span>
                                        </div>
                                        <div className="detail-data-item">
                                          <span className="detail-data-label detail-data-label-wide">生日：</span>
                                          <span className="detail-data-value">{selectedEmp?.birthday ?? ''}</span>
                                        </div>
                                        <div className="detail-data-item">
                                          <span className="detail-data-label detail-data-label-wide">任用資格：</span>
                                          <span className="detail-data-value">{selectedEmp?.currentPromotion?.jobroleTypeName ?? ''}</span>
                                        </div>
                                        <div className="detail-data-item">
                                          <span className="detail-data-label detail-data-label-wide">錄用類別：</span>
                                          <span className="detail-data-value">{selectedEmp?.currentPromotion?.categoryTypeName ?? ''}</span>
                                        </div>
                                      </div>
                                    </Col>
                                    <Col xs={24} md={12}>
                                      <div className="personnel-info-column">
                                        <div className="detail-data-item">
                                          <span className="detail-data-label detail-data-label-normal">帳號：</span>
                                          <span className="detail-data-value">{selectedEmp?.usersDTO.account ?? ''}</span>
                                        </div>
                                        <div className="detail-data-item">
                                          <span className="detail-data-label detail-data-label-normal">姓名：</span>
                                          <span className="detail-data-value-highlight">{selectedEmp?.usersDTO.name ?? ''}</span>
                                        </div>
                                        <div className="detail-data-item">
                                          <span className="detail-data-label detail-data-label-normal">服務部門：</span>
                                          <span
                                            className="detail-data-value detail-data-link"
                                            onClick={handleServiceDepartmentClick}
                                            style={{
                                              cursor: selectedUserId ? 'pointer' : 'default',
                                              color: selectedUserId ? '#1890ff' : 'inherit',
                                              textDecoration: selectedUserId ? 'underline' : 'none'
                                            }}
                                          >
                                            {(selectedEmp?.currentPromotion?.serviceDepartmentChange?.serviceDepartmentName &&
                                              (selectedEmp.currentPromotion.serviceDepartmentChange.serviceDepartmentName +
                                                (selectedEmp.currentPromotion.serviceDepartmentChange.serviceDivisionName ?
                                                  ` (${selectedEmp.currentPromotion.serviceDepartmentChange.serviceDivisionName})` : ''))) ||
                                              '點擊查看服務部門異動'}
                                          </span>
                                        </div>
                                        <div className="detail-data-item">
                                          <span className="detail-data-label detail-data-label-normal">職稱：</span>
                                          <span className="detail-data-value">{selectedEmp?.currentPromotion?.jobTitleName ?? ''}</span>
                                        </div>
                                        <div className="detail-data-item">
                                          <span className="detail-data-label detail-data-label-normal">職務：</span>
                                          <span className="detail-data-value">{selectedEmp?.usersDTO.positionName ?? ''}</span>
                                        </div>
                                      </div>
                                    </Col>
                                  </Row>
                                </Card>
                              </Col>

                              {/* 薪俸資料 */}
                              <Col xs={24} lg={8}>
                                <Card
                                  title={
                                    <div className="salary-info-title">
                                      <span className="material-icons">payments</span>
                                      薪俸資料
                                    </div>
                                  }
                                  className="salary-info-card"
                                  bodyStyle={{ padding: '20px' }}
                                >
                                  <div className="salary-info-column">
                                    <div className="detail-data-item">
                                      <span className="detail-data-label detail-data-label-normal">開支部門：</span>
                                      <span
                                        className="detail-data-value detail-data-link"
                                        onClick={handleExpenseDepartmentClick}
                                        style={{
                                          cursor: selectedUserId ? 'pointer' : 'default',
                                          color: selectedUserId ? '#1890ff' : 'inherit',
                                          textDecoration: selectedUserId ? 'underline' : 'none'
                                        }}
                                      >
                                        {selectedEmp?.currentPromotion?.expenseDepartmentChange?.expenseDepartmentName || '點擊查看開支部門異動'}
                                      </span>
                                    </div>
                                    <div className="detail-data-item">
                                      <span className="detail-data-label detail-data-label-normal">薪俸類型：</span>
                                      <span className="detail-data-value">{selectedEmp?.currentPromotion?.salaryTypeName ?? ''}</span>
                                    </div>
                                    <div className="detail-data-item">
                                      <span className="detail-data-label detail-data-label-normal">薪俸：</span>
                                      <span className="detail-data-value-salary">{
                                        selectedEmp?.currentPromotion?.salaryAmount ?
                                          (() => {
                                            const amount = parseFloat(selectedEmp.currentPromotion.salaryAmount);
                                            if (isNaN(amount)) return selectedEmp.currentPromotion.salaryAmount;

                                            // 根據薪俸類型決定顯示單位：薪點(1)顯示"點"，其他顯示"元"
                                            const unit = selectedEmp.currentPromotion.salaryType === '1' ? '點' : '元';
                                            return `${amount.toLocaleString()}${unit}`;
                                          })() : ''
                                      }</span>
                                    </div>
                                    <div className="detail-data-item">
                                      <span className="detail-data-label detail-data-label-normal">職等：</span>
                                      <span className="detail-data-value">{selectedEmp?.currentPromotion?.jobLevelName ?? ''}</span>
                                    </div>
                                    <div className="detail-data-item">
                                      <span className="detail-data-label detail-data-label-normal">級數：</span>
                                      <span className="detail-data-value">{selectedEmp?.currentPromotion?.jobRankName ?? ''}</span>
                                    </div>
                                  </div>
                                </Card>
                              </Col>
                            </Row>
                          </div>
                        ) : (
                          <div className="detail-empty-state">
                            <div className="detail-empty-icon">
                              <span className="material-icons">person_off</span>
                            </div>
                            無對應員工資料 請完成員工資料補全
                          </div>
                        );
                      })()
                    ) : (
                      <div className="detail-empty-state">
                        <div className="detail-empty-icon">
                          <span className="material-icons">person_search</span>
                        </div>
                        請先選取員工資料
                      </div>
                    )}
                  </Card>
                )}
              </>
            )}
          </Col>

          {/* 右邊功能選單 */}
          <Col xs={24} sm={24} md={24} lg={6} xl={6} style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
            transition: 'all 0.3s ease'
          }}>
            <Card
              className="function-menu-card"
              style={{
                background: 'linear-gradient(145deg, #ffffff, #f8fafc)',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
              }}
            >
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '12px'
              }}>
                <Button
                  type="primary"
                  icon={<MenuOutlined />}
                  block
                  className="function-button primary-function-button"
                  onClick={() => setIsModalVisible(true)}
                >
                  開啟功能選單
                </Button>

                <Button
                  type="default"
                  icon={<UserAddOutlined />}
                  block
                  className="function-button default-function-button"
                  onClick={handleAddClick}
                >
                  新增員工資料
                </Button>

                <Button
                  type="default"
                  icon={<EditOutlined />}
                  block
                  disabled={selectedUserId === ''}
                  className="function-button default-function-button"
                  onClick={employeeDetail?.userId === "" ? handlecompleteClick : handleEditClick}
                >
                  {employeeDetail?.userId === "" ? "補全員工資料" : "編輯員工資料"}
                </Button>

                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  block
                  disabled={selectedUserId === ''}
                  className={`function-button default-function-button ${loadingStates.refreshing ? 'button-loading' : ''}`}
                  onClick={handleRefresh}
                  loading={loadingStates.refreshing}
                >
                  重新載入資料
                </Button>

                <Popconfirm
                  title="確定要刪除這位員工資料嗎？"
                  onConfirm={() => setDeleteUserid(selectedUserId)}
                  okText="確認"
                  cancelText="取消"
                  placement="topRight"
                >
                  <Button
                    danger
                    icon={<DeleteOutlined />}
                    block
                    disabled={selectedUserId === ''}
                    className="function-button danger-function-button"
                  >
                    刪除員工資料
                  </Button>
                </Popconfirm>
              </div>
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 分頁控制自行顯示 loading 或 error， */}
      {/* 明細 */}


      <Card>
        <div className="tabs-container">
          <div className="tabs-header">
            <h3 className="tabs-title">員工資料詳情</h3>
            {loadingStates.employeeDetail && (
              <div className="dots-loading">
                <span></span>
                <span></span>
                <span></span>
              </div>
            )}
          </div>

          <Tabs
            type="card"
            items={allTabs.map(tab => ({
              key: tab.key,
              label: (
                <div style={{
                  padding: '10px 20px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  fontSize: '14px',
                  fontWeight: selectedTab === tab.key ? 600 : 400,
                  color: selectedTab === tab.key ? '#1890ff' : '#666',
                  transition: 'all 0.3s ease',
                  borderRadius: '6px',
                  position: 'relative'
                }}>
                  {tab.label}
                  {selectedTab === tab.key && (
                    <div style={{
                      position: 'absolute',
                      bottom: '-2px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      width: '20px',
                      height: '2px',
                      background: '#1890ff',
                      borderRadius: '2px'
                    }} />
                  )}
                </div>
              ),
            }))}
            activeKey={selectedTab}
            onChange={setSelectedTab}
            tabBarStyle={{
              marginBottom: '20px',
              borderBottom: '2px solid #e8e8e8',
              padding: '0 8px'
            }}
            tabBarGutter={16}
            animated={{ tabPane: true }}
          />

          <div className="tab-content" style={{ position: 'relative' }}>
            {selectedUserId ? (
              <>
                {loadingStates.employeeDetail && <SectionLoadingOverlay text="載入詳細資料..." />}

                {!loadingStates.employeeDetail && (
                  <>
                    {(employeeDetail && employeeDetail?.userId !== "") ? (
                      <>
                        <div style={{ display: selectedTab === 'employee' ? 'block' : 'none' }}>
                          <EmployeeInfo userId={selectedUserId} active={selectedTab === 'employee'} tabUpdateidx={tabUpdateIdx} />
                        </div>
                        <div style={{ display: selectedTab === 'promotion' ? 'block' : 'none' }}>
                          <PromotionInfo userId={selectedUserId} active={selectedTab === 'promotion'} tabUpdateidx={tabUpdateIdx} />
                        </div>
                        <div style={{ display: selectedTab === 'education' ? 'block' : 'none' }}>
                          <EducationInfo userId={selectedUserId} active={selectedTab === 'education'} tabUpdateidx={tabUpdateIdx} />
                        </div>
                        <div style={{ display: selectedTab === 'train' ? 'block' : 'none' }}>
                          <TrainInfo userId={selectedUserId} active={selectedTab === 'train'} tabUpdateidx={tabUpdateIdx} />
                        </div>
                        <div style={{ display: selectedTab === 'examination' ? 'block' : 'none' }}>
                          <ExaminationInfo userId={selectedUserId} active={selectedTab === 'examination'} tabUpdateidx={tabUpdateIdx} />
                        </div>
                        <div style={{ display: selectedTab === 'certification' ? 'block' : 'none' }}>
                          <CertificationInfo userId={selectedUserId} active={selectedTab === 'certification'} tabUpdateidx={tabUpdateIdx} />
                        </div>
                        <div style={{ display: selectedTab === 'undergo' ? 'block' : 'none' }}>
                          <UndergoInfo userId={selectedUserId} active={selectedTab === 'undergo'} tabUpdateidx={tabUpdateIdx} />
                        </div>
                        <div style={{ display: selectedTab === 'ensure' ? 'block' : 'none' }}>
                          <EnsureInfo userId={selectedUserId} active={selectedTab === 'ensure'} tabUpdateidx={tabUpdateIdx} />
                        </div>
                        <div style={{ display: selectedTab === 'suspend' ? 'block' : 'none' }}>
                          <SuspendInfo userId={selectedUserId} active={selectedTab === 'suspend'} tabUpdateidx={tabUpdateIdx} />
                        </div>
                        <div style={{ display: selectedTab === 'salary' ? 'block' : 'none' }}>
                          <SalaryInfo userId={selectedUserId} active={selectedTab === 'salary'} tabUpdateidx={tabUpdateIdx} />
                        </div>
                        <div style={{ display: selectedTab === 'hensure' ? 'block' : 'none' }}>
                          <HensureInfo userId={selectedUserId} active={selectedTab === 'hensure'} tabUpdateidx={tabUpdateIdx} />
                        </div>
                        <div style={{ display: selectedTab === 'dependent' ? 'block' : 'none' }}>
                          <DependentInfo userId={selectedUserId} active={selectedTab === 'dependent'} tabUpdateidx={tabUpdateIdx} />
                        </div>
                        <div style={{ display: selectedTab === 'performance' ? 'block' : 'none' }}>
                          <PerformancePointRecordInfo userId={selectedUserId} active={selectedTab === 'performance'} tabUpdateidx={tabUpdateIdx} />
                        </div>
                        <div style={{ display: selectedTab === 'regularSalary' ? 'block' : 'none' }}>
                          <RegularSalaryRecordInfo userId={selectedUserId} active={selectedTab === 'regularSalary'} tabUpdateidx={tabUpdateIdx} />
                        </div>
                        <div style={{ display: selectedTab === 'insuranceHistory' ? 'block' : 'none' }}>
                          <InsuranceHistoryInfo userId={selectedUserId} active={selectedTab === 'insuranceHistory'} tabUpdateidx={tabUpdateIdx} />
                        </div>
                      </>
                    ) : (
                      <div style={{
                        width: '100%',
                        height: '350px',
                        display: 'block',
                        position: 'relative',
                        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
                        borderRadius: '12px',
                        padding: '50px 20px',
                        boxSizing: 'border-box',
                        border: '2px solid #3b82f6',
                        boxShadow: '0 8px 24px rgba(59, 130, 246, 0.15)'
                      }}>
                        {/* 內容容器 */}
                        <div style={{
                          position: 'absolute',
                          top: '50%',
                          left: '50%',
                          transform: 'translate(-50%, -50%)',
                          textAlign: 'center',
                          width: '90%'
                        }}>
                          {/* 圖標 */}
                          <div style={{
                            width: '60px',
                            height: '60px',
                            background: '#3b82f6',
                            borderRadius: '50%',
                            margin: '0 auto 20px auto',
                            display: 'block',
                            position: 'relative'
                          }}>
                            <div style={{
                              position: 'absolute',
                              top: '50%',
                              left: '50%',
                              transform: 'translate(-50%, -50%)',
                              width: '30px',
                              height: '30px',
                              background: 'white',
                              borderRadius: '50%'
                            }}>
                              <div style={{
                                position: 'absolute',
                                top: '6px',
                                left: '50%',
                                transform: 'translateX(-50%)',
                                width: '12px',
                                height: '12px',
                                background: '#3b82f6',
                                borderRadius: '50%'
                              }} />
                              <div style={{
                                position: 'absolute',
                                bottom: '3px',
                                left: '50%',
                                transform: 'translateX(-50%)',
                                width: '18px',
                                height: '8px',
                                background: '#3b82f6',
                                borderRadius: '8px 8px 0 0'
                              }} />
                            </div>
                          </div>

                          <h2 style={{
                            fontSize: '24px',
                            fontWeight: 'bold',
                            color: '#1e293b',
                            margin: '0 0 12px 0'
                          }}>員工資料未建立</h2>

                          <p style={{
                            fontSize: '16px',
                            color: '#64748b',
                            margin: '0',
                            lineHeight: '1.5'
                          }}>請先建立完整的員工資料，或選取其他已有資料的員工</p>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </>
            ) : (
              <div className="empty-state" style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '300px',
                padding: '40px 20px',
                textAlign: 'center'
              }}>
                <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ marginBottom: '16px' }}>
                  <rect width="80" height="80" rx="40" fill="#F3F4F6" />
                  <path d="M40 28V52M28 40H52" stroke="#9CA3AF" strokeWidth="2" strokeLinecap="round" />
                </svg>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', alignItems: 'center' }}>
                  <span style={{
                    fontSize: '18px',
                    fontWeight: '600',
                    color: '#262626',
                    marginBottom: '4px'
                  }}>尚未選取員工</span>
                  <span style={{
                    fontSize: '14px',
                    color: '#8c8c8c'
                  }}>請先從左側列表選擇一位員工</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </Card>

      <Modal
        title="功能選單"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={800}
      >
        <TabsCard
          selectedKey={selectedTab}
          onSelect={setSelectedTab}
        />
      </Modal>

      <EditEmployeeForm
        userId={selectedUserId}
        mode={editStatus}
        visible={editModalVisible}
        onSuccess={handleEditSuccess}
        onCancel={handleEditCancel}
      />

      {/* 開支部門異動Modal */}
      <Modal
        title="開支部門異動資料"
        open={expenseDepartmentModalVisible}
        onCancel={handleExpenseDepartmentModalClose}
        footer={null}
        width={1200}
        style={{ top: 20 }}
        bodyStyle={{ padding: '20px' }}
      >
        {selectedUserId && (
          <ExpenseDepartmentChangeInfo
            userId={selectedUserId}
            active={expenseDepartmentModalVisible}
            tabUpdateidx={tabUpdateIdx}
          />
        )}
      </Modal>

      {/* 服務部門異動Modal */}
      <Modal
        title="服務部門異動資料"
        open={serviceDepartmentModalVisible}
        onCancel={handleServiceDepartmentModalClose}
        footer={null}
        width={1200}
        style={{ top: 20 }}
        bodyStyle={{ padding: '20px' }}
      >
        {selectedUserId && (
          <ServiceDepartmentChangeInfo
            userId={selectedUserId}
            active={serviceDepartmentModalVisible}
            tabUpdateidx={tabUpdateIdx}
          />
        )}
      </Modal>

      {deleteUserid && (
        <DeleteWithCountdown
          onDelete={async () => {
            try {
              await deleteEmployeeDetail(deleteUserid);
              setDeleteUserid(null);
            } catch (error) {
              message.error('刪除失敗，請稍後再試');
            }
          }}
          onCancel={() => setDeleteUserid(null)}
        />
      )}

      {/* 刪除中的全頁面 Loading */}
      {loadingStates.deleting && <PageLoadingOverlay text="刪除中，請稍候..." />}
    </div>
  );
};

export default EmployeePage;
