'use client';

import { Table, Spin, message, DatePicker, Button, Space, Modal } from 'antd';
import { useEffect, useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { getPerformancePointSummary, PerformancePointSummaryItem } from '@/services/pas/PerformancePoint/PerformancePointRecordService';
import PerformancePointRecordInfo from '@/app/pas/employee_main/employee_modules/performancePointRecord/PerformancePointRecordInfo'; // 匯入

const { RangePicker } = DatePicker;

const PerformancePointSummary = () => {
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState<PerformancePointSummaryItem[]>([]);

    const currentYear = dayjs().year();
    const defaultRange: [Dayjs, Dayjs] = [
        dayjs(`${currentYear}-01-01`),
        dayjs(`${currentYear}-12-31`)
    ];
    const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>(defaultRange);

    // 分頁狀態
    const [modalOpen, setModalOpen] = useState(false);
    const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
    const [selectedUserName, setSelectedUserName] = useState<string | null>(null);

    const handleRowClick = (record: PerformancePointSummaryItem) => {
        setSelectedUserId(record.userId);
        setSelectedUserName(record.userName); // 儲存員工姓名
        setModalOpen(true);
    };

    useEffect(() => {
        fetchSummary();
    }, []);

    // 每次 modal 關閉都重新抓資料
    useEffect(() => {
        if (!modalOpen) {
            fetchSummary();
        }
    }, [modalOpen]);

    const fetchSummary = async () => {
        try {
            setLoading(true);
            const startDate = dateRange[0].format('YYYY-MM-DD');
            const endDate = dateRange[1].format('YYYY-MM-DD');
            const groupUid = '';

            const res = await getPerformancePointSummary(groupUid, startDate, endDate);
            if (res.success && res.data) {
                setData(res.data);
            } else {
                message.error(res.message || '讀取點數總表失敗');
            }
        } catch (error) {
            console.error('fetchSummary error:', error);
            message.error('無法連線至伺服器，請稍後再試');
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <Space style={{ marginBottom: 16 }}>
                <RangePicker
                    value={dateRange}
                    onChange={(dates) => {
                        if (dates) setDateRange(dates as [Dayjs, Dayjs]);
                    }}
                    format="YYYY-MM-DD"
                />
                <Button type="primary" onClick={fetchSummary}>
                    查詢
                </Button>
            </Space>
            <Spin spinning={loading}>
                <Table
                    columns={[
                        {
                            title: '名次',
                            key: 'rank',
                            dataIndex: 'rank',
                            render: (value) => (
                                <span style={{
                                    fontWeight: 'bold',
                                    color: value <= 3 ? '#f5222d' : value <= 10 ? '#fa8c16' : '#52c41a',
                                    fontSize: '14px'
                                }}>
                                    {value}
                                </span>
                            ),
                            width: 80
                        },
                        {
                            title: '員工編號',
                            dataIndex: 'empNo',
                            key: 'empNo',
                            width: 100
                        },
                        {
                            title: '員工姓名',
                            dataIndex: 'userName',
                            key: 'userName',
                            width: 120
                        },
                        {
                            title: '服務部門',
                            key: 'serviceDepartment',
                            render: (_, record) => (
                                <div>
                                    <div>{record.serviceDepartmentName}</div>
                                    {record.serviceDivisionName && (
                                        <div style={{ fontSize: '12px', color: '#666' }}>
                                            {record.serviceDivisionName}
                                        </div>
                                    )}
                                </div>
                            ),
                            width: 150
                        },
                        {
                            title: '開支部門',
                            dataIndex: 'expenseDepartmentName',
                            key: 'expenseDepartmentName',
                            width: 120
                        },
                        {
                            title: '職稱',
                            dataIndex: 'jobTitleName',
                            key: 'jobTitleName',
                            width: 100
                        },
                        {
                            title: '職等/級數',
                            key: 'jobLevel',
                            render: (_, record) => (
                                <div>
                                    <div>{record.jobLevelName}</div>
                                    {record.jobRankName && (
                                        <div style={{ fontSize: '12px', color: '#666' }}>
                                            {record.jobRankName}
                                        </div>
                                    )}
                                </div>
                            ),
                            width: 100
                        },
                        {
                            title: '總點數',
                            dataIndex: 'totalPoint',
                            key: 'totalPoint',
                            render: (value) => (
                                <span style={{
                                    fontWeight: 'bold',
                                    color: '#1890ff',
                                    fontSize: '16px'
                                }}>
                                    {value.toFixed(2)}
                                </span>
                            ),
                            width: 100,
                            sorter: (a, b) => a.totalPoint - b.totalPoint,
                            defaultSortOrder: 'descend'
                        }
                    ]}
                    dataSource={data}
                    rowKey="userId"
                    onRow={(record) => ({
                        onClick: () => handleRowClick(record),
                        style: { cursor: 'pointer' }
                    })}
                    pagination={{
                        pageSize: 20,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 項，共 ${total} 項`
                    }}
                    scroll={{ x: 800 }}
                />
            </Spin>

            <Modal
                title={`${selectedUserName ?? ''}`}
                open={modalOpen}
                onCancel={() => {
                    setModalOpen(false);
                    setSelectedUserId(null);
                    setSelectedUserName(null);
                }}
                footer={null}
                width={1080} // 可調整寬度
                destroyOnClose
            >
                {selectedUserId && (
                    <PerformancePointRecordInfo userId={selectedUserId} active={modalOpen} />
                )}
            </Modal>

        </>
    );
};

export default PerformancePointSummary;
