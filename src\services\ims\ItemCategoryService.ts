import { apiEndpoints, ApiResponse } from "@/config/api";
import { httpClient } from "../http";
import { devLog, devSuccess, devWarn, LOG_SYMBOLS } from '@/utils/devLogger';

// 庫存品分類介面
export interface ItemCategory {
    itemCategoryID: string;
    name: string;
    description: string;
    parentID: string | null;
    sortCode: number;
    parent: ItemCategory | null;
    children: ItemCategory[];
    items: any[];
    createTime: number | null;
    createUserId: string | null;
    updateTime: number | null;
    updateUserId: string | null;
    deleteTime: number | null;
    deleteUserId: string | null;
    isDeleted: boolean;
}

export const createEmptyItemCategory = (): Partial<ItemCategory> => ({
    name: '',
    description: '',
    parentID: null,
    sortCode: 0,
});

/** 取得庫存品分類列表 */
export async function getItemCategoryList(): Promise<ApiResponse<ItemCategory[]>> {
    try {
        console.log('🔄 ItemCategoryService: 開始載入分類列表...');
        const response = await httpClient(apiEndpoints.getItemCategoryList, {
            method: "GET",
        });

        console.log(`✅ ItemCategoryService: API回應完成`, response);

        // 驗證回應資料格式
        if (response && response.success && response.data) {
            // 確保 data 是陣列
            if (!Array.isArray(response.data)) {
                console.warn('⚠️ ItemCategoryService: API回應的data不是陣列，嘗試修正:', response.data);
                return {
                    success: true,
                    message: "分類資料格式已修正",
                    data: [],
                };
            }

            // 驗證陣列中的每個項目
            const validCategories = response.data.filter(category => {
                return category &&
                       typeof category === 'object' &&
                       category.itemCategoryID &&
                       category.name;
            });

            if (validCategories.length !== response.data.length) {
                console.warn(`⚠️ ItemCategoryService: 過濾無效分類資料，原始: ${response.data.length}，有效: ${validCategories.length}`);
            }

            return {
                ...response,
                data: validCategories
            };
        }

        return response;
    } catch (error: any) {
        console.error('❌ ItemCategoryService: 載入分類列表時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "取得庫存品分類列表失敗",
            data: [],
        };
    }
}

/** 新增庫存品分類 */
export async function addItemCategory(category: Partial<ItemCategory>): Promise<ApiResponse<any>> {
    try {
        // 資料驗證
        if (!category.name || category.name.trim() === '') {
            return {
                success: false,
                message: "分類名稱不能為空",
            };
        }

        devLog(`${LOG_SYMBOLS.LOADING} ItemCategoryService: 新增分類...`, category.name);
        const categoryData = {
            name: category.name,
            description: category.description || '',
            parentID: category.parentID || null,
            sortCode: category.sortCode || 0,
        };

        const response = await httpClient(apiEndpoints.addItemCategory, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(categoryData),
        });

        if (response.success) {
            devSuccess('ItemCategoryService: 分類新增成功');
        } else {
            devWarn('ItemCategoryService: 分類新增失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ ItemCategoryService: 新增分類時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "新增庫存品分類失敗",
        };
    }
}

/** 修改庫存品分類 */
export async function editItemCategory(category: Partial<ItemCategory>): Promise<ApiResponse<any>> {
    try {
        // 檢查必要的ID
        if (!category.itemCategoryID) {
            return {
                success: false,
                message: "分類ID不能為空",
            };
        }

        // 資料驗證
        if (!category.name || category.name.trim() === '') {
            return {
                success: false,
                message: "分類名稱不能為空",
            };
        }

        devLog(`${LOG_SYMBOLS.LOADING} ItemCategoryService: 更新分類...`, category.name);
        const categoryData = {
            itemCategoryID: category.itemCategoryID,
            name: category.name,
            description: category.description || '',
            parentID: category.parentID || null,
            sortCode: category.sortCode || 0,
        };

        const response = await httpClient(apiEndpoints.editItemCategory, {
            method: "PATCH",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(categoryData),
        });

        if (response.success) {
            devSuccess('ItemCategoryService: 分類更新成功');
        } else {
            devWarn('ItemCategoryService: 分類更新失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ ItemCategoryService: 更新分類時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "修改庫存品分類失敗",
        };
    }
}

/** 刪除庫存品分類 */
export async function deleteItemCategory(categoryId: string): Promise<ApiResponse<any>> {
    try {
        if (!categoryId || typeof categoryId !== 'string') {
            return {
                success: false,
                message: "分類ID不能為空",
            };
        }

        console.log('🔄 ItemCategoryService: 刪除分類...', categoryId);
        const response = await httpClient(apiEndpoints.deleteItemCategory, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({ itemCategoryID: categoryId }),
        });

        if (response.success) {
            console.log('✅ ItemCategoryService: 分類刪除成功');
        } else {
            console.warn('⚠️ ItemCategoryService: 分類刪除失敗:', response.message);
        }

        return response;
    } catch (error: any) {
        console.error('❌ ItemCategoryService: 刪除分類時發生錯誤:', error);
        return {
            success: false,
            message: error.message || "刪除庫存品分類失敗",
        };
    }
}

/** 建構分類樹狀結構 */
export function buildCategoryTree(categories: ItemCategory[]): ItemCategory[] {
    // 防禦性程式設計：確保 categories 是有效的陣列
    if (!categories || !Array.isArray(categories)) {
        console.warn('⚠️ buildCategoryTree: categories 不是有效的陣列:', categories);
        return [];
    }

    const categoryMap = new Map<string, ItemCategory>();
    const rootCategories: ItemCategory[] = [];

    try {
        // 建立映射
        categories.forEach(category => {
            if (category && category.itemCategoryID) {
                categoryMap.set(category.itemCategoryID, { ...category, children: [] });
            }
        });

        // 建構樹狀結構
        categories.forEach(category => {
            if (!category || !category.itemCategoryID) return;

            const categoryNode = categoryMap.get(category.itemCategoryID);
            if (categoryNode) {
                if (category.parentID && categoryMap.has(category.parentID)) {
                    const parent = categoryMap.get(category.parentID);
                    if (parent) {
                        parent.children.push(categoryNode);
                    }
                } else {
                    rootCategories.push(categoryNode);
                }
            }
        });
    } catch (error) {
        console.error('❌ buildCategoryTree: 建構樹狀結構時發生錯誤:', error);
        return [];
    }

    return rootCategories;
}

/** 取得分類路徑 */
export function getCategoryPath(categories: ItemCategory[], categoryId: string): string[] {
    // 防禦性程式設計：確保 categories 是有效的陣列
    if (!categories || !Array.isArray(categories)) {
        console.warn('⚠️ getCategoryPath: categories 不是有效的陣列:', categories);
        return [];
    }

    if (!categoryId || typeof categoryId !== 'string') {
        console.warn('⚠️ getCategoryPath: categoryId 無效:', categoryId);
        return [];
    }

    const path: string[] = [];
    const categoryMap = new Map<string, ItemCategory>();

    try {
        categories.forEach(category => {
            if (category && category.itemCategoryID) {
                categoryMap.set(category.itemCategoryID, category);
            }
        });

        let currentCategory = categoryMap.get(categoryId);
        while (currentCategory) {
            path.unshift(currentCategory.name);
            currentCategory = currentCategory.parentID ? categoryMap.get(currentCategory.parentID) : undefined;
        }
    } catch (error) {
        console.error('❌ getCategoryPath: 取得分類路徑時發生錯誤:', error);
        return [];
    }

    return path;
}

/** 取得分類層級顯示名稱 (Ex:大 / 中 / 小) */
export function getCategoryHierarchyName(categories: ItemCategory[], categoryId: string | null): string {
    // 防禦性程式設計：確保參數有效
    if (!categoryId || typeof categoryId !== 'string') return '-';
    if (!categories || !Array.isArray(categories)) {
        console.warn('⚠️ getCategoryHierarchyName: categories 不是有效的陣列:', categories);
        return '-';
    }

    try {
        const path = getCategoryPath(categories, categoryId);
        return path.length > 0 ? path.join(' / ') : '-';
    } catch (error) {
        console.error('❌ getCategoryHierarchyName: 取得分類層級名稱時發生錯誤:', error);
        return '-';
    }
}