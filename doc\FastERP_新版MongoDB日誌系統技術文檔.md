# FastERP 新版 MongoDB 日誌系統技術文檔

## 📋 **系統概述**

### **設計目標**
新版 MongoDB 日誌系統旨在解決現有系統的根本性問題，提供穩定、可靠、高效能的企業級日誌記錄解決方案。

### **核心改進**
- ✅ **徹底解決循環引用問題**: 智能序列化機制，完全避免 Entity Framework 導航屬性循環引用
- ✅ **強化連接管理**: 專用連接管理器，提供連接池、健康檢查和自動重連機制
- ✅ **簡化資料結構**: 清晰易讀的日誌格式，避免複雜的 _t/_v 嵌套結構
- ✅ **彈性錯誤處理**: 熔斷器模式、重試機制和降級處理，確保業務操作不受影響
- ✅ **完整審計追蹤**: 詳細的 before/after 資料記錄，支援複雜實體層次結構

---

## 🏗️ **系統架構**

### **核心組件**

#### **1. IMongoDBConnectionManager - 連接管理器**
```csharp
/// <summary>
/// MongoDB 連接管理器
/// 提供連接池管理、健康檢查和重連機制
/// </summary>
public interface IMongoDBConnectionManager
{
    Task<IMongoCollection<T>> GetCollectionAsync<T>(string collectionName);
    Task<bool> HealthCheckAsync();
    Task<bool> ResetConnectionAsync();
    Task<MongoDBConnectionStatus> GetConnectionStatusAsync();
}
```

**主要功能:**
- 自動連接池管理
- 定期健康檢查
- 連接失敗自動重連
- 連接狀態監控

#### **2. ILogDataProcessor - 資料處理器**
```csharp
/// <summary>
/// 日誌資料處理器
/// 負責將複雜的實體變更轉換為簡化的日誌格式
/// </summary>
public interface ILogDataProcessor
{
    SimpleLogEntry ProcessEntityChange(EntityChangeRecord record);
    string SafeSerialize(object? data);
    List<SimpleLogEntry> ProcessBatchChanges(EntityChangeSnapshot snapshot, string transactionId);
}
```

**主要功能:**
- 安全序列化，避免循環引用
- 敏感資料過濾
- 批次變更處理
- 統一資料格式化

#### **3. IResilientLoggerService - 彈性日誌服務**
```csharp
/// <summary>
/// 具有彈性和錯誤處理能力的日誌服務
/// 提供重試機制、熔斷器模式和降級處理
/// </summary>
public interface IResilientLoggerService : ILoggerService
{
    Task<bool> TryLogAsync(SimpleLogEntry entry, int maxRetries = 3);
    Task<LoggingHealthStatus> GetHealthStatusAsync();
    void EnableCircuitBreaker(bool enabled);
    Task ResetCircuitBreakerAsync();
}
```

**主要功能:**
- 智能重試機制
- 熔斷器保護
- 降級處理
- 健康狀態監控

### **資料流程**
```
實體變更 → EnhancedEntityChangeTracker → EntityChangeRecord
    ↓
LogDataProcessor → 安全序列化 → SimpleLogEntry
    ↓
ResilientLoggerService → 重試/熔斷器 → MongoDB
    ↓
連接管理器 → 健康檢查 → 成功寫入
```

---

## 📊 **資料模型**

### **SimpleLogEntry - 簡化日誌記錄**
```csharp
public class SimpleLogEntry
{
    public Guid Id { get; set; }                    // 唯一識別碼
    public DateTime Timestamp { get; set; }         // 時間戳（台灣時區）
    public string Level { get; set; }               // 日誌級別
    public string Message { get; set; }             // 日誌訊息
    public string Source { get; set; }              // 來源系統
    public string? TransactionId { get; set; }      // 交易識別碼
    
    // 資料內容
    public LogData? Data { get; set; }              // 簡化的資料結構
    
    // 上下文資訊
    public string? UserId { get; set; }             // 使用者識別碼
    public string? IpAddress { get; set; }          // IP 地址
    public string? RequestUrl { get; set; }         // 請求 URL
    
    // 錯誤資訊
    public string? ErrorMessage { get; set; }       // 錯誤訊息
    public string? StackTrace { get; set; }         // 堆疊追蹤
    
    // 實體變更資訊
    public string? EntityType { get; set; }         // 實體類型
    public string? EntityId { get; set; }           // 實體識別碼
    public string? Operation { get; set; }          // 操作類型
}
```

### **LogData - 簡化資料結構**
```csharp
public class LogData
{
    public string? Operation { get; set; }          // 操作類型
    public string? BeforeData { get; set; }         // 變更前資料（JSON）
    public string? AfterData { get; set; }          // 變更後資料（JSON）
    public List<string>? ChangedFields { get; set; } // 變更欄位列表
    public string? Summary { get; set; }            // 變更摘要
    public Dictionary<string, string>? Metadata { get; set; } // 中繼資料
    public string Status { get; set; }              // 處理狀態
    public long? ProcessingTimeMs { get; set; }     // 處理時間
}
```

### **資料格式範例**
```json
{
  "_id": "12345678-1234-1234-1234-123456789012",
  "timestamp": "2024-12-12T14:30:00+08:00",
  "level": "Information",
  "message": "修改 Partner 實體，變更欄位: Name, UpdateTime",
  "source": "EntityFramework",
  "transactionId": "87654321-4321-4321-4321-210987654321",
  "data": {
    "operation": "UPDATE",
    "beforeData": "{\"Name\":\"舊名稱\",\"UpdateTime\":\"2024-12-12T14:29:00\"}",
    "afterData": "{\"Name\":\"新名稱\",\"UpdateTime\":\"2024-12-12T14:30:00\"}",
    "changedFields": ["Name", "UpdateTime"],
    "summary": "修改 Partner 實體，變更 2 個欄位",
    "status": "Success",
    "processingTimeMs": 45
  },
  "userId": "user123",
  "ipAddress": "*************",
  "requestUrl": "https://api.fasterp.com/api/partner/update",
  "entityType": "Partner",
  "entityId": "partner-001",
  "operation": "UPDATE"
}
```

---

## ⚙️ **配置設定**

### **appsettings.json 配置**
```json
{
  "MongoDB": {
    "ConnectionString": "mongodb://sa:<EMAIL>:27017/?authMechanism=SCRAM-SHA-256",
    "DatabaseName": "FAST_ERP",
    "CollectionName": "Logger_New"
  },
  "Logging": {
    "Resilient": {
      "RetryOptions": {
        "MaxRetries": 3,
        "BaseDelayMs": 1000,
        "MaxDelayMs": 30000,
        "UseExponentialBackoff": true,
        "UseJitter": true
      },
      "CircuitBreaker": {
        "FailureThreshold": 5,
        "SuccessThreshold": 3,
        "TimeoutMs": 60000,
        "MonitoringWindowMs": 300000,
        "Enabled": true
      }
    },
    "Migration": {
      "EnableNewService": true,
      "EnableLegacyService": false,
      "FailOnLegacyError": false,
      "FailOnNewError": false
    },
    "DataProcessing": {
      "IncludeSensitiveData": false,
      "MaxSerializationDepth": 3,
      "CompressLargeData": true,
      "LargeDataThreshold": 10000,
      "IncludeDetailedChanges": true
    }
  }
}
```

### **Program.cs 服務註冊**
```csharp
// MongoDB 連接管理
builder.Services.AddSingleton<IMongoDBConnectionManager, MongoDBConnectionManager>();

// 資料處理
builder.Services.AddSingleton<LogProcessingOptions>();
builder.Services.AddSingleton<ILogDataProcessor, LogDataProcessor>();

// 彈性日誌服務
builder.Services.AddSingleton<IResilientLoggerService, ResilientMongoDBLoggerService>();

// 主要日誌服務（生產環境）
builder.Services.AddSingleton<ILoggerService, ResilientMongoDBLoggerService>();

// 遷移期間的雙寫服務（可選）
// builder.Services.AddSingleton<ILoggerService, DualWriteLoggerService>();
```

---

## 🔧 **使用方式**

### **基本日誌記錄**
```csharp
public class PartnerService
{
    private readonly ILoggerService _logger;
    
    public PartnerService(ILoggerService logger)
    {
        _logger = logger;
    }
    
    public async Task<Partner> CreatePartnerAsync(PartnerDto dto)
    {
        try
        {
            // 業務邏輯
            var partner = new Partner { /* ... */ };
            
            // 記錄操作日誌
            await _logger.LogInfoAsync($"新增夥伴: {partner.Name}", "PartnerService");
            
            return partner;
        }
        catch (Exception ex)
        {
            // 記錄錯誤日誌
            await _logger.LogErrorAsync("新增夥伴失敗", ex, "PartnerService");
            throw;
        }
    }
}
```

### **實體變更自動記錄**
```csharp
// ERPDbContext 中已自動整合，無需額外程式碼
public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
{
    // 系統自動捕獲實體變更並記錄日誌
    var result = await base.SaveChangesAsync(cancellationToken);
    return result;
}
```

### **健康狀態檢查**
```csharp
public class HealthController : ControllerBase
{
    private readonly IResilientLoggerService _logger;
    
    [HttpGet("logging-health")]
    public async Task<IActionResult> GetLoggingHealth()
    {
        var health = await _logger.GetHealthStatusAsync();
        return Ok(health);
    }
}
```

---

## 🔍 **監控和維護**

### **健康檢查指標**
- **連接狀態**: MongoDB 連接是否正常
- **成功率**: 日誌記錄成功率（目標 > 99.5%）
- **回應時間**: 平均日誌記錄時間（目標 < 100ms）
- **熔斷器狀態**: 熔斷器當前狀態
- **錯誤統計**: 各類錯誤的統計資訊

### **API 端點**
```
GET  /api/LoggingMigration/health/new-system     - 獲取系統健康狀態
GET  /api/LoggingMigration/health/error-statistics - 獲取錯誤統計
POST /api/LoggingMigration/test/basic-functionality - 測試基本功能
POST /api/LoggingMigration/test/performance      - 效能測試
POST /api/LoggingMigration/circuit-breaker/reset - 重置熔斷器
```

### **MongoDB 查詢範例**
```javascript
// 查詢特定實體的變更記錄
db.Logger_New.find({
  "entityType": "Partner",
  "entityId": "partner-001"
}).sort({ "timestamp": -1 })

// 查詢特定時間範圍的錯誤日誌
db.Logger_New.find({
  "level": "Error",
  "timestamp": {
    $gte: ISODate("2024-12-12T00:00:00Z"),
    $lte: ISODate("2024-12-12T23:59:59Z")
  }
})

// 查詢特定交易的所有相關日誌
db.Logger_New.find({
  "transactionId": "87654321-4321-4321-4321-210987654321"
}).sort({ "timestamp": 1 })
```

---

## 🚀 **效能特性**

### **效能指標**
- **單一日誌記錄**: < 50ms
- **批次日誌記錄**: < 200ms (100條)
- **並發處理能力**: > 1000 TPS
- **記憶體使用**: < 100MB (正常負載)
- **連接池效率**: 95% 連接重用率

### **優化機制**
- **連接池管理**: 自動管理 MongoDB 連接，避免頻繁建立/關閉連接
- **批次處理**: 支援批次日誌寫入，提升吞吐量
- **非同步處理**: 完全非同步操作，不阻塞主執行緒
- **智能快取**: 實體元數據快取，減少反射操作
- **資料壓縮**: 大型資料自動壓縮，節省儲存空間

---

## 🛡️ **安全性**

### **資料保護**
- **敏感資料過濾**: 自動過濾密碼、金鑰等敏感欄位
- **資料脫敏**: 支援自定義敏感欄位清單
- **存取控制**: 基於角色的日誌存取權限
- **資料加密**: 支援 MongoDB 傳輸加密

### **隱私合規**
- **GDPR 合規**: 支援個人資料匿名化
- **資料保留**: 可配置的日誌保留期限
- **審計追蹤**: 完整的操作審計記錄
- **資料完整性**: 防篡改的日誌記錄機制

---

## 📈 **故障排除**

### **常見問題**

#### **1. MongoDB 連接失敗**
```
症狀: 日誌記錄失敗，健康檢查顯示連接異常
解決: 檢查 MongoDB 服務狀態，驗證連接字串，重置連接
API: POST /api/LoggingMigration/circuit-breaker/reset
```

#### **2. 熔斷器開啟**
```
症狀: 日誌記錄被阻止，系統進入降級模式
解決: 檢查 MongoDB 狀態，修復問題後重置熔斷器
API: POST /api/LoggingMigration/circuit-breaker/reset
```

#### **3. 效能下降**
```
症狀: 日誌記錄時間過長，系統回應緩慢
解決: 檢查 MongoDB 效能，調整批次大小，優化查詢
監控: GET /api/LoggingMigration/health/error-statistics
```

### **診斷工具**
- **健康檢查 API**: 即時系統狀態
- **效能測試 API**: 系統效能驗證
- **錯誤統計 API**: 詳細錯誤分析
- **MongoDB 監控**: 資料庫層面監控

---

## 📚 **最佳實踐**

### **開發建議**
1. **適當的日誌級別**: 使用正確的日誌級別，避免過度記錄
2. **有意義的訊息**: 提供清晰、有用的日誌訊息
3. **交易識別碼**: 使用一致的交易識別碼關聯相關操作
4. **錯誤處理**: 妥善處理日誌記錄失敗，不影響業務邏輯

### **維運建議**
1. **定期監控**: 定期檢查系統健康狀態和效能指標
2. **容量規劃**: 根據日誌增長趨勢規劃儲存容量
3. **備份策略**: 建立完整的日誌備份和恢復策略
4. **效能調優**: 根據實際使用情況調整系統參數

### **安全建議**
1. **存取控制**: 限制日誌資料的存取權限
2. **資料保護**: 確保敏感資料得到適當保護
3. **合規性**: 遵循相關的資料保護法規
4. **審計**: 定期審計日誌系統的安全性

---

## 🎯 **總結**

新版 FastERP MongoDB 日誌系統提供了企業級的可靠性、效能和功能性，徹底解決了舊系統的根本問題：

✅ **穩定可靠**: 熔斷器和重試機制確保系統穩定運行
✅ **高效能**: 優化的架構提供卓越的效能表現
✅ **易維護**: 清晰的架構和完整的監控工具
✅ **可擴展**: 模組化設計支援未來功能擴展
✅ **安全合規**: 完整的安全機制和合規性支援

此系統為 FastERP 提供了堅實的日誌基礎設施，支援企業的長期發展需求。
