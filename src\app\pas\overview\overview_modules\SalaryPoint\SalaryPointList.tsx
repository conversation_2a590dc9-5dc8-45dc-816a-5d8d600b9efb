import React, { useEffect, useState, useMemo } from "react";
import { Table, Button, Space, message, Popconfirm, Typography, Modal, Card, Tabs, Select, Tag, Alert, Row, Col, Tooltip } from "antd";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import {
    getSalaryPointList,
    deleteSalaryPoint,
    SalaryPoint,
    getDepartmentSalaryPointList,
    deleteDepartmentSalaryPoint,
    DepartmentSalaryPoint,
} from "@/services/pas/SalaryPointService";
import { getDepartments, Department } from "@/services/common/departmentService";
import SalaryPointEditor from "./SalaryPointEditor";
import DepartmentSalaryPointEditor from "./DepartmentSalaryPointEditor";
import DeleteWithCountdown from '@/app/pas/components/DeleteWithCountdown';
import {
    EditOutlined,
    DeleteOutlined,
    ExclamationCircleOutlined,
    PlusOutlined,
    DollarOutlined,
    CalendarOutlined,
    InfoCircleOutlined,
    GlobalOutlined,
    TeamOutlined,
    BankOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    FilterOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 狀態篩選選項
const STATUS_FILTER_OPTIONS = [
    { label: '全部', value: 'all' },
    { label: '生效中', value: 'active' },
    { label: '未生效', value: 'future' },
    { label: '已過期', value: 'expired' }
];

// 找出在指定日期當前生效的薪點（當前日期在生效日期之後的第一筆）
const findCurrentActiveSalaryPoint = (salaryPoints: (SalaryPoint | DepartmentSalaryPoint)[], targetDate = dayjs().startOf('day')) => {
    if (!salaryPoints || salaryPoints.length === 0) return null;

    // 篩選出有生效日期且生效日期 <= 目標日期的記錄
    const validPoints = salaryPoints
        .filter(point => point.effectiveDate && dayjs(point.effectiveDate).startOf('day').isBefore(targetDate) || dayjs(point.effectiveDate).startOf('day').isSame(targetDate))
        .sort((a, b) => dayjs(b.effectiveDate).valueOf() - dayjs(a.effectiveDate).valueOf()); // 按生效日期降序排列

    // 返回最近的一筆（即當前生效的）
    return validPoints.length > 0 ? validPoints[0] : null;
};

// 檢查薪點是否在指定日期生效
const isSalaryPointActiveOnDate = (salaryPoint: SalaryPoint | DepartmentSalaryPoint, salaryPoints: (SalaryPoint | DepartmentSalaryPoint)[], targetDate = dayjs().startOf('day')) => {
    const currentActive = findCurrentActiveSalaryPoint(salaryPoints, targetDate);
    return currentActive?.uid === salaryPoint.uid;
};

// 獲取薪點狀態
const getSalaryPointStatus = (salaryPoint: SalaryPoint | DepartmentSalaryPoint, salaryPoints: (SalaryPoint | DepartmentSalaryPoint)[]) => {
    const today = dayjs().startOf('day');
    if (!salaryPoint.effectiveDate) {
        return { status: 'unknown', label: '未設定', color: 'default' };
    }

    const effectiveDate = dayjs(salaryPoint.effectiveDate).startOf('day');

    if (effectiveDate.isAfter(today)) {
        return { status: 'future', label: '未生效', color: 'blue' };
    } else if (isSalaryPointActiveOnDate(salaryPoint, salaryPoints, today)) {
        return { status: 'active', label: '生效中', color: 'green' };
    } else {
        return { status: 'expired', label: '已過期', color: 'default' };
    }
};

// 格式化日期顯示
const formatDateDisplay = (dateStr: string | null, emptyText = '-') => {
    return dateStr ? dayjs(dateStr).format('YYYY-MM-DD') : emptyText;
};

const SalaryPointList: React.FC = () => {
    // 全局薪點相關狀態
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState<SalaryPoint[]>([]);
    const [selectedUid, setSelectedUid] = useState<string | null>(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [deleteUid, setDeleteUid] = useState<string | null>(null);

    // 部門薪點相關狀態
    const [deptLoading, setDeptLoading] = useState(false);
    const [deptData, setDeptData] = useState<DepartmentSalaryPoint[]>([]);
    const [selectedDeptUid, setSelectedDeptUid] = useState<string | null>(null);
    const [deptModalVisible, setDeptModalVisible] = useState(false);
    const [deleteDeptUid, setDeleteDeptUid] = useState<string | null>(null);

    // 部門相關狀態
    const [departments, setDepartments] = useState<Department[]>([]);
    const [selectedDepartmentId, setSelectedDepartmentId] = useState<string | undefined>();

    // 篩選相關狀態
    const [globalStatusFilter, setGlobalStatusFilter] = useState<string>('active');
    const [deptStatusFilter, setDeptStatusFilter] = useState<string>('active');

    // 其他狀態
    const [activeTab, setActiveTab] = useState("global");

    // 載入部門列表
    const loadDepartments = async () => {
        try {
            const result = await getDepartments();
            if (result.success) {
                setDepartments(result.data || []);
            }
        } catch (error: any) {
            message.error(error.message || "載入部門列表失敗");
        }
    };

    // 載入全局薪點列表
    const loadList = async () => {
        setLoading(true);
        try {
            const result = await getSalaryPointList();
            if (result.success) {
                setData(result.data || []);
            } else {
                message.error(result.message || "載入薪點資料失敗");
            }
        } catch (error: any) {
            message.error(error.message || "載入薪點資料失敗");
        } finally {
            setLoading(false);
        }
    };

    // 載入部門薪點列表
    const loadDeptList = async () => {
        setDeptLoading(true);
        try {
            const result = await getDepartmentSalaryPointList();
            if (result.success) {
                // 合併部門名稱
                const dataWithDeptNames = (result.data || []).map(item => ({
                    ...item,
                    departmentName: departments.find(dept => dept.departmentId === item.departmentId)?.name || item.departmentId
                }));
                setDeptData(dataWithDeptNames);
            } else {
                message.error(result.message || "載入部門薪點資料失敗");
            }
        } catch (error: any) {
            message.error(error.message || "載入部門薪點資料失敗");
        } finally {
            setDeptLoading(false);
        }
    };

    // 點擊編輯全局薪點
    const handleEdit = (uid: string) => {
        setSelectedUid(uid);
        setModalVisible(true);
    };

    // 點擊刪除全局薪點
    const handleDelete = async (uid: string) => {
        try {
            const result = await deleteSalaryPoint(uid);
            if (result.success) {
                message.success("刪除成功");
                loadList();
            } else {
                message.error(result.message || "刪除失敗");
            }
        } catch (error: any) {
            message.error(error.message || "刪除失敗");
        }
    };

    // 點擊新增全局薪點
    const handleAdd = () => {
        setSelectedUid(null);
        setModalVisible(true);
    };

    // 點擊編輯部門薪點
    const handleDeptEdit = (uid: string) => {
        setSelectedDeptUid(uid);
        setDeptModalVisible(true);
    };

    // 點擊刪除部門薪點
    const handleDeptDelete = async (uid: string) => {
        try {
            const result = await deleteDepartmentSalaryPoint(uid);
            if (result.success) {
                message.success("刪除成功");
                loadDeptList();
            } else {
                message.error(result.message || "刪除失敗");
            }
        } catch (error: any) {
            message.error(error.message || "刪除失敗");
        }
    };

    // 點擊新增部門薪點
    const handleDeptAdd = () => {
        setSelectedDeptUid(null);
        setDeptModalVisible(true);
    };

    // 全局薪點表格欄位定義
    const columns: ColumnsType<SalaryPoint> = [
        {
            title: "薪點名稱",
            dataIndex: "pointLevel",
            width: 120,
            render: (text) => (
                <Space>
                    <DollarOutlined style={{ color: '#1890ff' }} />
                    <Text strong>{text}</Text>
                </Space>
            ),
        },
        {
            title: "薪點金額",
            dataIndex: "amount",
            width: 120,
            render: (value: number) => (
                <Text type="success">
                    {value?.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}
                </Text>
            ),
        },
        {
            title: "生效日期",
            dataIndex: "effectiveDate",
            width: 120,
            render: (text) => (
                <Space>
                    <CalendarOutlined style={{ color: '#722ed1' }} />
                    <Text>{formatDateDisplay(text)}</Text>
                </Space>
            ),
        },
        {
            title: "狀態",
            width: 100,
            render: (_, record) => {
                const status = getSalaryPointStatus(record, data);
                return (
                    <Tag
                        color={status.color}
                        icon={status.status === 'active' ? <CheckCircleOutlined /> : <ClockCircleOutlined />}
                    >
                        {status.label}
                    </Tag>
                );
            },
        },
        {
            title: "調整原因",
            dataIndex: "adjustmentReason",
            ellipsis: true,
            render: (text) => (
                <Space>
                    <InfoCircleOutlined style={{ color: '#faad14' }} />
                    <Text>{text}</Text>
                </Space>
            ),
        },
        {
            title: "操作",
            key: "action",
            render: (_, record) => (
                <Space>
                    <Button
                        type="text"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(record.uid)}
                    >
                        編輯
                    </Button>
                    <Popconfirm
                        title={
                            <div>
                                <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                                <Text>確定要刪除此筆資料嗎？</Text>
                            </div>
                        }
                        onConfirm={() => setDeleteUid(record.uid)}
                        okText="確認"
                        cancelText="取消"
                        okButtonProps={{ danger: true }}
                    >
                        <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                        >
                            刪除
                        </Button>
                    </Popconfirm>
                </Space>
            ),
        },
    ];

    // 部門薪點表格欄位定義
    const deptColumns: ColumnsType<DepartmentSalaryPoint> = [
        {
            title: "部門",
            dataIndex: "departmentName",
            width: 120,
            render: (text, record) => (
                <Space>
                    <BankOutlined style={{ color: '#52c41a' }} />
                    <Tag color="blue">{text || record.departmentId}</Tag>
                </Space>
            ),
        },
        {
            title: "薪點名稱",
            dataIndex: "pointLevel",
            width: 120,
            render: (text) => (
                <Space>
                    <DollarOutlined style={{ color: '#1890ff' }} />
                    <Text strong>{text}</Text>
                </Space>
            ),
        },
        {
            title: "薪點金額",
            dataIndex: "amount",
            width: 120,
            render: (value: number) => (
                <Text type="success">
                    {value?.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}
                </Text>
            ),
        },
        {
            title: "生效日期",
            dataIndex: "effectiveDate",
            width: 120,
            render: (text) => (
                <Space>
                    <CalendarOutlined style={{ color: '#722ed1' }} />
                    <Text>{formatDateDisplay(text)}</Text>
                </Space>
            ),
        },
        {
            title: "狀態",
            width: 100,
            render: (_, record) => {
                const status = getSalaryPointStatus(record, deptData);
                return (
                    <Tag
                        color={status.color}
                        icon={status.status === 'active' ? <CheckCircleOutlined /> : <ClockCircleOutlined />}
                    >
                        {status.label}
                    </Tag>
                );
            },
        },
        {
            title: "調整原因",
            dataIndex: "adjustmentReason",
            ellipsis: true,
            render: (text) => (
                <Space>
                    <InfoCircleOutlined style={{ color: '#faad14' }} />
                    <Text>{text}</Text>
                </Space>
            ),
        },
        {
            title: "操作",
            key: "action",
            render: (_, record) => (
                <Space>
                    <Button
                        type="text"
                        icon={<EditOutlined />}
                        onClick={() => handleDeptEdit(record.uid)}
                    >
                        編輯
                    </Button>
                    <Popconfirm
                        title={
                            <div>
                                <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />
                                <Text>確定要刪除此筆資料嗎？</Text>
                            </div>
                        }
                        onConfirm={() => setDeleteDeptUid(record.uid)}
                        okText="確認"
                        cancelText="取消"
                        okButtonProps={{ danger: true }}
                    >
                        <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                        >
                            刪除
                        </Button>
                    </Popconfirm>
                </Space>
            ),
        },
    ];

    useEffect(() => {
        loadDepartments();
    }, []);

    useEffect(() => {
        if (departments.length > 0) {
            // 總是載入部門薪點資料，以便在概覽中顯示統計
            loadDeptList();
        }
    }, [departments]);

    useEffect(() => {
        if (activeTab === "global") {
            loadList();
        }
    }, [activeTab]);

    // 根據狀態篩選全局薪點資料
    const filteredGlobalData = useMemo(() => {
        let filtered = data;

        if (globalStatusFilter !== 'all') {
            filtered = filtered.filter(item => {
                const status = getSalaryPointStatus(item, data);
                return status.status === globalStatusFilter;
            });
        }

        return filtered;
    }, [data, globalStatusFilter]);

    // 根據選擇的部門和狀態過濾部門薪點資料
    const filteredDeptData = useMemo(() => {
        let filtered = deptData;

        // 先根據部門篩選
        if (selectedDepartmentId) {
            filtered = filtered.filter(item => item.departmentId === selectedDepartmentId);
        }

        // 再根據狀態篩選
        if (deptStatusFilter !== 'all') {
            filtered = filtered.filter(item => {
                const status = getSalaryPointStatus(item, deptData);
                return status.status === deptStatusFilter;
            });
        }

        return filtered;
    }, [deptData, selectedDepartmentId, deptStatusFilter]);

    // 獲取當前生效的薪點（用於概覽顯示）
    const currentActiveGlobalSalaryPoint = useMemo(() => {
        return data.find(item => getSalaryPointStatus(item, data).status === 'active') || null;
    }, [data]);

    const currentActiveDeptSalaryPoints = useMemo(() => {
        const activeDeptPoints: Record<string, DepartmentSalaryPoint> = {};

        // 找出每個部門的當前生效薪點
        departments.forEach(dept => {
            const deptActivePoint = deptData.find(item =>
                item.departmentId === dept.departmentId &&
                getSalaryPointStatus(item, deptData).status === 'active'
            );
            if (deptActivePoint) {
                activeDeptPoints[dept.departmentId] = deptActivePoint;
            }
        });

        return activeDeptPoints;
    }, [deptData, departments]);

    return (
        <div style={{ padding: '24px' }}>
            {/* 當前生效薪點概覽 */}
            <Card
                title={
                    <Space>
                        <InfoCircleOutlined />
                        <Title level={4} style={{ margin: 0 }}>當前生效薪點概覽</Title>
                    </Space>
                }
                className="shadow-sm"
                style={{ marginBottom: 16, borderRadius: '8px' }}
                loading={loading}
            >
                <Row gutter={[16, 16]}>
                    <Col xs={24} sm={12} lg={8}>
                        <Card
                            size="small"
                            style={{
                                borderLeft: `4px solid #1890ff`,
                                backgroundColor: currentActiveGlobalSalaryPoint ? '#f6ffed' : '#f5f5f5'
                            }}
                        >
                            <Space direction="vertical" style={{ width: '100%' }}>
                                <Space>
                                    <GlobalOutlined style={{ color: '#1890ff' }} />
                                    <Text strong style={{ color: '#1890ff' }}>全域薪點</Text>
                                </Space>

                                {currentActiveGlobalSalaryPoint ? (
                                    <>
                                        <div>
                                            <Text type="secondary">薪點名稱：</Text>
                                            <Text strong>{currentActiveGlobalSalaryPoint.pointLevel}</Text>
                                        </div>
                                        <div>
                                            <Text type="secondary">薪點金額：</Text>
                                            <Text strong style={{ color: '#52c41a' }}>
                                                NT$ {currentActiveGlobalSalaryPoint.amount?.toLocaleString('zh-TW', { minimumFractionDigits: 2 })}
                                            </Text>
                                        </div>
                                        <div>
                                            <Text type="secondary">生效日期：</Text>
                                            <Text>{formatDateDisplay(currentActiveGlobalSalaryPoint.effectiveDate)}</Text>
                                        </div>
                                        <Tag color="green">
                                            <CheckCircleOutlined style={{ marginRight: 4 }} />
                                            生效中
                                        </Tag>
                                    </>
                                ) : (
                                    <>
                                        <Text type="secondary">目前無生效薪點</Text>
                                        <Tag color="default">未設定</Tag>
                                    </>
                                )}
                            </Space>
                        </Card>
                    </Col>
                    <Col xs={24} sm={12} lg={16}>
                        <Card
                            size="small"
                            style={{
                                borderLeft: `4px solid #52c41a`,
                                backgroundColor: '#f6ffed'
                            }}
                        >
                            <Space direction="vertical" style={{ width: '100%' }}>
                                <Space>
                                    <TeamOutlined style={{ color: '#52c41a' }} />
                                    <Text strong style={{ color: '#52c41a' }}>部門薪點統計</Text>
                                </Space>
                                <Row gutter={[16, 8]}>
                                    <Col span={8}>
                                        <div>
                                            <Text type="secondary">總部門數：</Text>
                                            <Text strong>{departments.length}</Text>
                                        </div>
                                    </Col>
                                    <Col span={8}>
                                        <div>
                                            <Text type="secondary">已設定部門：</Text>
                                            <Text strong style={{ color: '#52c41a' }}>{Object.keys(currentActiveDeptSalaryPoints).length}</Text>
                                        </div>
                                    </Col>
                                    <Col span={8}>
                                        <div>
                                            <Text type="secondary">未設定部門：</Text>
                                            <Text strong style={{ color: '#fa8c16' }}>
                                                {departments.length - Object.keys(currentActiveDeptSalaryPoints).length}
                                            </Text>
                                        </div>
                                    </Col>
                                </Row>
                            </Space>
                        </Card>
                    </Col>
                </Row>
            </Card>

            <Card
                className="shadow-sm"
                style={{ borderRadius: '8px' }}
            >
                <div style={{ marginBottom: 16 }}>
                    <Title level={4} style={{ margin: 0, marginBottom: 16 }}>薪點金額管理</Title>

                    <Tabs
                        activeKey={activeTab}
                        onChange={setActiveTab}
                        style={{ marginTop: 16 }}
                    >
                        <TabPane
                            tab={
                                <span>
                                    <GlobalOutlined />
                                    全域薪點設定
                                </span>
                            }
                            key="global"
                        >
                            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 16 }}>
                                <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                                    <Text type="secondary">管理全公司通用的薪點金額設定</Text>
                                    <Select
                                        placeholder="篩選狀態"
                                        value={globalStatusFilter}
                                        onChange={setGlobalStatusFilter}
                                        style={{ width: 120 }}
                                        suffixIcon={<FilterOutlined />}
                                        options={STATUS_FILTER_OPTIONS}
                                    />
                                </div>
                                <Button
                                    type="primary"
                                    icon={<PlusOutlined />}
                                    onClick={handleAdd}
                                    style={{ borderRadius: '6px' }}
                                >
                                    新增薪點
                                </Button>
                            </div>

                            <Table
                                rowKey="uid"
                                columns={columns}
                                dataSource={filteredGlobalData}
                                loading={loading}
                                scroll={{ x: 1100 }}
                                rowClassName={(record) =>
                                    record.uid === deleteUid ? 'row-deleting-pulse' : ''
                                }
                                pagination={{
                                    showSizeChanger: true,
                                    showQuickJumper: true,
                                    showTotal: (total, range) => `共 ${total} 筆，顯示第 ${range?.[0]}-${range?.[1]} 筆`,
                                    style: { marginTop: 16 }
                                }}
                            />
                        </TabPane>

                        <TabPane
                            tab={
                                <span>
                                    <TeamOutlined />
                                    部門薪點設定
                                </span>
                            }
                            key="department"
                        >
                            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 16 }}>
                                <div style={{ display: 'flex', alignItems: 'center', gap: 16, flexWrap: 'wrap' }}>
                                    <Text type="secondary">管理各部門專屬的薪點金額設定</Text>
                                    <Select
                                        placeholder="選擇部門進行篩選"
                                        allowClear
                                        style={{ width: 200 }}
                                        value={selectedDepartmentId}
                                        onChange={setSelectedDepartmentId}
                                        options={departments.map(dept => ({
                                            label: dept.name,
                                            value: dept.departmentId
                                        }))}
                                    />
                                    <Select
                                        placeholder="篩選狀態"
                                        value={deptStatusFilter}
                                        onChange={setDeptStatusFilter}
                                        style={{ width: 120 }}
                                        suffixIcon={<FilterOutlined />}
                                        options={STATUS_FILTER_OPTIONS}
                                    />
                                </div>
                                <Button
                                    type="primary"
                                    icon={<PlusOutlined />}
                                    onClick={handleDeptAdd}
                                    style={{ borderRadius: '6px' }}
                                >
                                    新增部門薪點
                                </Button>
                            </div>

                            {/* 部門薪點統計資訊 */}
                            <Alert
                                message="部門薪點設定說明"
                                description={
                                    <div>
                                        <Text>部門薪點設定會覆蓋全域薪點設定。若部門沒有設定專屬薪點，則使用全域薪點。</Text>
                                        <br />
                                        <Text type="secondary">
                                            目前已設定 <Text strong style={{ color: '#52c41a' }}>{Object.keys(currentActiveDeptSalaryPoints).length}</Text> 個部門，
                                            未設定 <Text strong style={{ color: '#fa8c16' }}>{departments.length - Object.keys(currentActiveDeptSalaryPoints).length}</Text> 個部門
                                        </Text>
                                    </div>
                                }
                                type="info"
                                showIcon
                                style={{ marginBottom: 16 }}
                            />

                            <Table
                                rowKey="uid"
                                columns={deptColumns}
                                dataSource={filteredDeptData}
                                loading={deptLoading}
                                scroll={{ x: 1300 }}
                                rowClassName={(record) =>
                                    record.uid === deleteDeptUid ? 'row-deleting-pulse' : ''
                                }
                                pagination={{
                                    showSizeChanger: true,
                                    showQuickJumper: true,
                                    showTotal: (total, range) => `共 ${total} 筆，顯示第 ${range?.[0]}-${range?.[1]} 筆`,
                                    style: { marginTop: 16 }
                                }}
                                locale={{
                                    emptyText: selectedDepartmentId
                                        ? `所選部門暫無薪點設定，使用全域薪點設定`
                                        : '暫無部門薪點設定'
                                }}
                            />
                        </TabPane>
                    </Tabs>
                </div>
            </Card>

            {/* 全域薪點編輯對話框 */}
            <Modal
                title={
                    <Title level={5} style={{ margin: 0 }}>
                        {selectedUid ? "編輯薪點資料" : "新增薪點資料"}
                    </Title>
                }
                open={modalVisible}
                onCancel={() => setModalVisible(false)}
                footer={null}
                width={480}
                centered
                maskClosable={false}
                destroyOnClose
                styles={{
                    header: {
                        marginBottom: 0,
                        padding: '16px 24px',
                        borderBottom: '1px solid #f0f0f0'
                    },
                    body: {
                        padding: '24px'
                    }
                }}
            >
                <SalaryPointEditor
                    uid={selectedUid ?? undefined}
                    onSuccess={() => {
                        setModalVisible(false);
                        loadList();
                    }}
                    onCancel={() => setModalVisible(false)}
                />
            </Modal>

            {/* 部門薪點編輯對話框 */}
            <Modal
                title={
                    <Title level={5} style={{ margin: 0 }}>
                        {selectedDeptUid ? "編輯部門薪點資料" : "新增部門薪點資料"}
                    </Title>
                }
                open={deptModalVisible}
                onCancel={() => setDeptModalVisible(false)}
                footer={null}
                width={520}
                centered
                maskClosable={false}
                destroyOnClose
                styles={{
                    header: {
                        marginBottom: 0,
                        padding: '16px 24px',
                        borderBottom: '1px solid #f0f0f0'
                    },
                    body: {
                        padding: '24px'
                    }
                }}
            >
                <DepartmentSalaryPointEditor
                    uid={selectedDeptUid ?? undefined}
                    onSuccess={() => {
                        setDeptModalVisible(false);
                        loadDeptList();
                    }}
                    onCancel={() => setDeptModalVisible(false)}
                />
            </Modal>

            {/* 全域薪點刪除確認 */}
            {deleteUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        try {
                            await handleDelete(deleteUid);
                            setDeleteUid(null);
                        } catch (error) {
                            message.error('刪除失敗，請稍後再試');
                        }
                    }}
                    onCancel={() => setDeleteUid(null)}
                />
            )}

            {/* 部門薪點刪除確認 */}
            {deleteDeptUid && (
                <DeleteWithCountdown
                    onDelete={async () => {
                        try {
                            await handleDeptDelete(deleteDeptUid);
                            setDeleteDeptUid(null);
                        } catch (error) {
                            message.error('刪除失敗，請稍後再試');
                        }
                    }}
                    onCancel={() => setDeleteDeptUid(null)}
                />
            )}
        </div>
    );
};

export default SalaryPointList;
