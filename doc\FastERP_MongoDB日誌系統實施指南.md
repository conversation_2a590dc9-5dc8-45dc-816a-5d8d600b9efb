# FastERP MongoDB 日誌系統實施指南

## 🎯 **實施概述**

本指南提供 FastERP MongoDB 日誌系統從設計到部署的完整實施步驟，確保系統能夠穩定、可靠地運行在生產環境中。

---

## 📋 **實施前檢查清單**

### **環境準備**
- [ ] MongoDB 服務正常運行（版本 4.4+）
- [ ] .NET 8.0 運行環境
- [ ] Docker 環境（如使用容器部署）
- [ ] 網路連接正常（MongoDB 連接埠 27017）

### **權限確認**
- [ ] MongoDB 資料庫讀寫權限
- [ ] 應用程式部署權限
- [ ] 配置檔案修改權限
- [ ] 日誌檔案存取權限

### **備份準備**
- [ ] 現有 MongoDB 日誌資料備份
- [ ] 應用程式配置備份
- [ ] 程式碼版本控制確認

---

## 🔧 **步驟 1: 服務註冊配置**

### **1.1 更新 Program.cs**

在 `Program.cs` 中添加新版日誌服務註冊：

```csharp
#region Enhanced Logging Services
// 註冊新版彈性MongoDB日誌服務
builder.Services.AddSingleton<IMongoDBConnectionManager, MongoDBConnectionManager>();
builder.Services.AddSingleton<LogProcessingOptions>();
builder.Services.AddSingleton<ILogDataProcessor, LogDataProcessor>();
builder.Services.AddSingleton<IResilientLoggerService, ResilientMongoDBLoggerService>();

// 主要日誌服務（使用新版彈性服務）
builder.Services.AddSingleton<ILoggerService, ResilientMongoDBLoggerService>();
#endregion
```

### **1.2 配置選項設定**

在 `appsettings.json` 中添加新版日誌系統配置：

```json
{
  "MongoDB": {
    "ConnectionString": "mongodb://sa:<EMAIL>:27017/?authMechanism=SCRAM-SHA-256",
    "DatabaseName": "FAST_ERP",
    "CollectionName": "Logger_New"
  },
  "Logging": {
    "Resilient": {
      "RetryOptions": {
        "MaxRetries": 3,
        "BaseDelayMs": 1000,
        "MaxDelayMs": 30000,
        "UseExponentialBackoff": true,
        "UseJitter": true
      },
      "CircuitBreaker": {
        "FailureThreshold": 5,
        "SuccessThreshold": 3,
        "TimeoutMs": 60000,
        "MonitoringWindowMs": 300000,
        "Enabled": true
      }
    }
  }
}
```

---

## 🗄️ **步驟 2: MongoDB 資料庫準備**

### **2.1 建立新集合**

連接到 MongoDB 並執行以下指令：

```javascript
// 切換到 FastERP 資料庫
use FAST_ERP

// 建立新的日誌集合
db.createCollection("Logger_New")

// 建立索引以提升查詢效能
db.Logger_New.createIndex({ "timestamp": -1 })
db.Logger_New.createIndex({ "level": 1 })
db.Logger_New.createIndex({ "entityType": 1, "entityId": 1 })
db.Logger_New.createIndex({ "transactionId": 1 })
db.Logger_New.createIndex({ "userId": 1 })
db.Logger_New.createIndex({ "source": 1 })

// 建立複合索引
db.Logger_New.createIndex({ "entityType": 1, "timestamp": -1 })
db.Logger_New.createIndex({ "level": 1, "timestamp": -1 })
```

### **2.2 驗證集合建立**

```javascript
// 檢查集合是否建立成功
db.getCollectionNames()

// 檢查索引是否建立成功
db.Logger_New.getIndexes()
```

---

## 🚀 **步驟 3: 應用程式部署**

### **3.1 編譯驗證**

```bash
# 清理並重建專案
dotnet clean
dotnet build

# 檢查編譯錯誤
dotnet build --verbosity normal
```

### **3.2 單元測試執行**

```bash
# 執行所有測試
dotnet test

# 執行特定的日誌相關測試
dotnet test --filter "Category=Logging"
```

### **3.3 部署到測試環境**

```bash
# 發布應用程式
dotnet publish -c Release -o ./publish

# Docker 部署（如適用）
docker build -t fasterp-backend .
docker run -d -p 5000:80 fasterp-backend
```

---

## 🔍 **步驟 4: 功能驗證**

### **4.1 基本功能測試**

使用 API 端點進行功能驗證：

```bash
# 測試基本功能
curl -X POST "http://localhost:5000/api/LoggingMigration/test/basic-functionality"

# 檢查健康狀態
curl -X GET "http://localhost:5000/api/LoggingMigration/health/new-system"

# 效能測試
curl -X POST "http://localhost:5000/api/LoggingMigration/test/performance?logCount=100"
```

### **4.2 實體變更測試**

```bash
# 測試實體變更日誌記錄
curl -X POST "http://localhost:5000/api/LoggingTest/test-create"
curl -X PUT "http://localhost:5000/api/LoggingTest/test-update/{id}"
curl -X DELETE "http://localhost:5000/api/LoggingTest/test-delete/{id}"
```

### **4.3 MongoDB 資料驗證**

```javascript
// 檢查日誌是否正確寫入
db.Logger_New.find().limit(10).sort({ "timestamp": -1 })

// 檢查資料格式
db.Logger_New.findOne()

// 統計日誌數量
db.Logger_New.count()
```

---

## 📊 **步驟 5: 效能監控設定**

### **5.1 健康檢查端點**

確保以下端點正常運作：

```
GET  /api/LoggingMigration/health/new-system
GET  /api/LoggingMigration/health/error-statistics
POST /api/LoggingMigration/test/basic-functionality
POST /api/LoggingMigration/test/performance
```

### **5.2 監控指標設定**

建立監控告警規則：

```yaml
# 範例監控規則（Prometheus/Grafana）
- alert: LoggingSystemDown
  expr: logging_health_status == 0
  for: 5m
  labels:
    severity: critical
  annotations:
    summary: "日誌系統異常"

- alert: LoggingHighErrorRate
  expr: logging_error_rate > 5
  for: 10m
  labels:
    severity: warning
  annotations:
    summary: "日誌錯誤率過高"
```

---

## 🔄 **步驟 6: 遷移執行（可選）**

### **6.1 雙寫模式啟用**

如需從舊系統平滑遷移，可啟用雙寫模式：

```csharp
// 在 Program.cs 中
builder.Services.AddSingleton<ILoggerService, DualWriteLoggerService>();
```

```json
// 在 appsettings.json 中
{
  "Logging": {
    "Migration": {
      "EnableNewService": true,
      "EnableLegacyService": true,
      "FailOnLegacyError": false,
      "FailOnNewError": false
    }
  }
}
```

### **6.2 資料遷移腳本**

```javascript
// 遷移現有資料到新格式
db.Logger.find().forEach(function(doc) {
    var newDoc = {
        _id: doc._id,
        timestamp: doc.CreateTime || new Date(),
        level: doc.LogLevel || "Information",
        message: doc.Message || "",
        source: doc.Source || "System",
        transactionId: doc.TransactionId,
        data: {
            operation: "MIGRATED",
            summary: "從舊系統遷移的資料",
            beforeData: doc.Data ? JSON.stringify(doc.Data) : null,
            status: "Migrated"
        },
        userId: doc.UserId,
        ipAddress: doc.IpAddress,
        requestUrl: doc.RequestUrl,
        userAgent: doc.UserAgent,
        errorMessage: doc.Exception,
        stackTrace: doc.StackTrace
    };
    
    db.Logger_New.insertOne(newDoc);
});
```

---

## 🛠️ **步驟 7: 故障排除**

### **7.1 常見問題診斷**

#### **連接問題**
```bash
# 檢查 MongoDB 連接
curl -X GET "http://localhost:5000/api/LoggingMigration/health/new-system"

# 重置連接
curl -X POST "http://localhost:5000/api/LoggingMigration/circuit-breaker/reset"
```

#### **效能問題**
```bash
# 效能測試
curl -X POST "http://localhost:5000/api/LoggingMigration/test/performance?logCount=50"

# 檢查錯誤統計
curl -X GET "http://localhost:5000/api/LoggingMigration/health/error-statistics"
```

### **7.2 日誌分析**

```bash
# 檢查應用程式日誌
tail -f /var/log/fasterp/application.log

# 檢查 MongoDB 日誌
tail -f /var/log/mongodb/mongod.log
```

---

## ✅ **步驟 8: 驗收測試**

### **8.1 功能驗收**

- [ ] 基本日誌記錄功能正常
- [ ] 實體變更自動記錄
- [ ] 錯誤日誌包含完整資訊
- [ ] 健康檢查端點回應正常
- [ ] 熔斷器機制正確運作

### **8.2 效能驗收**

- [ ] 單一日誌記錄 < 100ms
- [ ] 批次日誌記錄 < 500ms (100條)
- [ ] 系統可用性 > 99%
- [ ] 錯誤率 < 1%

### **8.3 資料完整性驗收**

- [ ] 日誌格式正確
- [ ] 時間戳準確
- [ ] 實體變更資料完整
- [ ] 敏感資料正確過濾

---

## 📚 **步驟 9: 文檔更新**

### **9.1 技術文檔**

- [ ] API 文檔更新
- [ ] 架構圖更新
- [ ] 配置說明更新
- [ ] 故障排除指南更新

### **9.2 操作手冊**

- [ ] 部署流程文檔
- [ ] 監控設定指南
- [ ] 維護操作手冊
- [ ] 緊急處理程序

---

## 🎯 **步驟 10: 上線準備**

### **10.1 最終檢查**

- [ ] 所有測試通過
- [ ] 效能指標達標
- [ ] 監控告警設定完成
- [ ] 備份恢復程序驗證
- [ ] 團隊培訓完成

### **10.2 上線計畫**

1. **準備階段**: 確認所有前置條件
2. **部署階段**: 執行生產環境部署
3. **驗證階段**: 執行上線後驗證
4. **監控階段**: 持續監控系統狀態
5. **優化階段**: 根據實際使用情況調優

---

## 📞 **支援聯絡**

### **技術支援**
- **開發團隊**: <EMAIL>
- **維運團隊**: <EMAIL>
- **緊急聯絡**: <EMAIL>

### **文檔資源**
- **技術文檔**: `/doc/FastERP_新版MongoDB日誌系統技術文檔.md`
- **API 文檔**: `/swagger/index.html`
- **遷移指南**: `/doc/MongoDB_Logging_Migration_Plan.md`

---

## 🎉 **實施完成**

恭喜！您已成功實施 FastERP 新版 MongoDB 日誌系統。

**主要成果:**
✅ 穩定可靠的日誌記錄系統
✅ 完整的錯誤處理和恢復機制
✅ 高效能的資料處理能力
✅ 全面的監控和診斷工具
✅ 企業級的安全性和合規性

系統現在已準備好支援 FastERP 的長期發展需求！
